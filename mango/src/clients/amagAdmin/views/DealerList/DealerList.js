/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { List } from 'immutable';
import PropTypes from 'prop-types';
import withStyles from 'isomorphic-style-loader/lib/withStyles';

import combineHOCs from '../../../../tango/hoc/combineHOCs';
import { getDealerImports, getDealers } from '../../modules/dealer/selectors';
import { find, getDealerImport, runDealerImport, downloadDealerImportXml } from '../../modules/dealer/actions';
import { restrictAccess } from '../../modules/RestrictAccess';
import { CONFIG } from '../DealerConfig/constants';
import DealerItem from './DealerItem';
import { withAsyncReduxData } from '../../../../tango/withAsyncReduxData';
import { getRequestIsCompleted, getRequestIsFetching } from '../../../../modules/request/selectors';
import { extractAndSort } from '../../../../modules/post/documents/util';

import styles from './dealerList.css';
import { formatDateTime } from '../../../amagCore/modules/formatters/formatDate';

type Props = {
	dealers: List<any>,
	isCompleted: boolean,
	onRunDealerImport: Function,
	onDownloadDealerImportXml: Function,
	isRunDealerImportCompleted: boolean,
	isRunDealerImportFetching: boolean,
	isDownloadDealerImportXmlCompleted: boolean,
	isDownloadDealerImportXmlFetching: boolean,
	dealerImport: Map,
	getDealerImport: Function,
	findDealers: Function,
};

const POLLING_INTERVAL = 10000; // 10 seconds

class DealerList extends React.Component<Props> {
	componentDidMount() {
		// No need to check here - componentDidUpdate will handle it when data loads
	}

	componentDidUpdate(prevProps: Props) {
		const prevDealerImport = prevProps.dealerImport;
		const currentDealerImport = this.props.dealerImport;

		// Handle case where dealerImport data becomes available for the first time
		if (!prevDealerImport && currentDealerImport) {
			// Data just loaded - check if import is in progress
			if (currentDealerImport.get('status') === 'in-progress') {
				this.startPolling();
			}
			return;
		}

		// Only proceed if both dealer imports are available
		if (!prevDealerImport || !currentDealerImport) {
			return;
		}

		const prevImportInProgress = prevDealerImport.get('status') === 'in-progress';
		const currentImportInProgress = currentDealerImport.get('status') === 'in-progress';

		// Start polling when import starts, stop when it finishes
		if (!prevImportInProgress && currentImportInProgress) {
			this.startPolling();
		} else if (prevImportInProgress && !currentImportInProgress) {
			this.stopPolling();
		}
	}

	componentWillUnmount() {
		this.stopPolling();
	}

	interval: ?number = undefined;

	startPolling = () => {
		if (this.interval) {
			clearInterval(this.interval);
		}
		this.interval = setInterval(() => {
			if (this.props.getDealerImport) {
				this.props.getDealerImport();
			}
		}, POLLING_INTERVAL);
	};

	stopPolling = () => {
		if (this.interval) {
			clearInterval(this.interval);
			this.interval = undefined;
		}
	};

	render() {
		const {
			dealers,
			isCompleted,
			dealerImport,
			onRunDealerImport,
			onDownloadDealerImportXml,
			isRunDealerImportCompleted,
			isRunDealerImportFetching,
			isDownloadDealerImportXmlCompleted,
			isDownloadDealerImportXmlFetching,
		} = this.props;

		const getImportStatus = () => dealerImport.get('status');
		const isImportInProgress = () => getImportStatus() === 'in-progress';
		const isImportSuccess = () => getImportStatus() === 'success';
		const isImportFailed = () => getImportStatus() === 'failed';

		const isButtonDisabled = () => {
			const importInProgress = isImportInProgress();
			const runImportCompleted = isRunDealerImportCompleted === undefined
				? true
				: isRunDealerImportCompleted;
			return importInProgress || !runImportCompleted || isRunDealerImportFetching;
		};

		const isDownloadButtonDisabled = () => {
			const downloadCompleted = isDownloadDealerImportXmlCompleted === undefined
				? true
				: isDownloadDealerImportXmlCompleted;
			return !downloadCompleted || isDownloadDealerImportXmlFetching;
		};

		const getButtonClassName = () => {
			if (isImportInProgress()) return `${styles.dealerImportButton} ${styles.inProgress}`;
			if (isImportSuccess()) return `${styles.dealerImportButton} ${styles.success}`;
			if (isImportFailed()) return `${styles.dealerImportButton} ${styles.failed}`;
			return styles.dealerImportButton;
		};

		const getButtonText = () => {
			if (isImportInProgress()) return 'Sync in progress...';
			return 'Sync dealers with XML';
		};

		return (
			<div>
				<div>
					{dealerImport ? (
						<div className={styles.dealerImport}>
							<button
								disabled={isDownloadButtonDisabled()}
								className={`${styles.dealerImportButton} ${styles.download}`}
								onClick={onDownloadDealerImportXml}
								title="Download last XML file"
							>
								<span className="vwIcon" data-icon="&#xe926;" />
							</button>
							<button
								disabled={isButtonDisabled()}
								className={getButtonClassName()}
								onClick={onRunDealerImport}
							>
								{getButtonText()}
							</button>

							<div className={styles.dealerImportInfo}>
								{dealerImport.get('finished-at') && (
									<div className={styles.infoItem}>
										Last sync at <strong>{formatDateTime(dealerImport.get('finished-at'))}</strong>
									</div>
								)}
								{dealerImport.get('started-at') && (
									<div className={styles.infoItem}>
										Sync started at <strong>{formatDateTime(dealerImport.get('started-at'))}</strong>
									</div>
								)}
								{dealerImport.get('filename') && (
									<div className={styles.infoItem}>
										Last synced file <strong>{dealerImport.get('filename')}</strong>
									</div>
								)}
							</div>
						</div>
					) : (
						<div className={styles.loading} />
					)}
				</div>
				<div className={styles.configContainer}>
					{isCompleted ? (
						<table className={styles.dealerTable}>
							<thead>
								<tr>
									<th>Status</th>
									<th>Code</th>
									<th>Dealer Label</th>
									<th>Name 1</th>
									<th>Name 2</th>
									<th>Short Name</th>
									{CONFIG.brands.map(brand => {
										let header = brand.name;
										if (brand.name === 'Nutzfahrzeuge') {
											// `Nutzfahrzeuge` is too long - make it shorter
											header = 'VW NF';
										} else if (brand.name === 'Volkswagen') {
											header = 'VW';
										}

										return (
											<th
												key={brand.id}
												className={styles.brandHeader}
												title={header}
											>
												<span>{header}</span>
											</th>
										);
									})}
									<th>Website</th>
								</tr>
							</thead>
							<tbody>
								{dealers &&
									dealers
										.sort(
											extractAndSort(item =>
												item.getIn(['dealer', 'identity', '0']),
											),
										)
										.map(item => (
											<DealerItem
												key={item.getIn(['dealer', 'identity'])}
												identity={item.getIn(['dealer', 'identity'])}
												dealerLabel={item.getIn(['dealer', 'dealer-label'])}
												dealerGroupLabels={item.getIn([
													'dealer',
													'dealer-group-labels',
												])}
												data={item.getIn(['dealer', 'config'])}
											/>
										))}
							</tbody>
							<tfoot>
								<tr>
									<th>Count</th>
									<th>{dealers.size}</th>
								</tr>
							</tfoot>
						</table>
					) : (
						<div className={styles.loading}>Loading...</div>
					)}
				</div>
			</div>
		);
	}
}

const mapStateToProps = (state: ReduxState): Object => ({
	dealers: getDealers(state),
	isCompleted: getRequestIsCompleted(state, 'findDealers'),
	dealerImport: getDealerImports(state),
	isDealerImportCompleted: getRequestIsCompleted(state, 'getDealerImport'),
	isRunDealerImportCompleted: getRequestIsCompleted(state, 'runDealerImport'),
	isRunDealerImportFetching: getRequestIsFetching(state, 'runDealerImport'),
	isDownloadDealerImportXmlCompleted: getRequestIsCompleted(state, 'downloadDealerImportXml'),
	isDownloadDealerImportXmlFetching: getRequestIsFetching(state, 'downloadDealerImportXml'),
});

const mapDispatchToProps = (dispatch: Function) => ({
	findDealers: () => dispatch(find()),
	getDealerImport: () => dispatch(getDealerImport()),
	onRunDealerImport: () => dispatch(runDealerImport()),
	onDownloadDealerImportXml: () => dispatch(downloadDealerImportXml()),
});

function requestData({
	findDealers,
	getDealerImport: getDealerImportAction,
}: {
	findDealers: () => void,
	getDealerImportAction: () => void,
}) {
	findDealers();
	getDealerImportAction();
}

const isDone = ({ isCompleted }): boolean => isCompleted;

const withHocs = combineHOCs([
	restrictAccess,
	withStyles(styles),
	withAsyncReduxData(requestData, isDone, mapStateToProps, mapDispatchToProps),
	connect(
		mapStateToProps,
		mapDispatchToProps,
	),
]);

DealerList.propTypes = {
	dealers: PropTypes.instanceOf(List),
	isCompleted: PropTypes.bool,
	onRunDealerImport: PropTypes.func,
	onDownloadDealerImportXml: PropTypes.func,
	isRunDealerImportCompleted: PropTypes.bool,
	isRunDealerImportFetching: PropTypes.bool,
	isDownloadDealerImportXmlCompleted: PropTypes.bool,
	isDownloadDealerImportXmlFetching: PropTypes.bool,
	dealerImport: PropTypes.instanceOf(Map),
	getDealerImport: PropTypes.func,
};

export default withHocs(DealerList);
