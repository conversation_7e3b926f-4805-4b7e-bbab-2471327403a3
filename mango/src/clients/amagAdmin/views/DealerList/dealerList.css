@import '../../assets/variables.css';

.configContainer {
	font-family: var(--font-text-light);
}

.dealerTable {
	width: 100%;
	border-collapse: collapse;
	text-align: left;

	& th,
	& td {
		padding: 10px;
		font-size: var(--caption-font-size);
		line-height: 1.1;
	}

	& thead {
		position: -webkit-sticky;
		position: sticky;
		top: 0;
		z-index: 2;

		background: var(--white);
		box-shadow: 0 5px 5px 0 var(--light-gray);

		& th {
			font-family: var(--font-text-bold);

			&:nth-child(1) {
				padding-left: 20px;
			}

			&.brandHeader {
				max-width: 50px;
				padding-left: 0;
				padding-right: 0;

				& span {
					display: block;
					overflow: hidden;
					max-width: 50px;
					text-align: center;
					text-overflow: ellipsis;
				}
			}
		}
	}

	& tbody {
		& td {
			border-bottom: 1px solid rgb(235, 235, 235);

			&:nth-child(1) {
				padding-left: 20px;
				font-family: var(--font-text-bold);
			}

			&:nth-child(2) {
				max-width: 7em;
				overflow: auto;
			}
		}

		& tr:nth-child(even) {
			background: var(--white);
		}

		& img {
			width: 30px;
			height: 30px;
		}
	}

	& .noPadding {
		padding: 0;
	}
}

.loading {
	@apply --loading;
}

.dealerImport {
	@apply --box;
	margin-bottom: 20px;
	background: var(--white);
	border: 1px solid var(--grey-border);
	display: flex;
	align-items: center;
	gap: 20px;
	padding: 20px;
}

.dealerImportButtons {
	display: flex;
	align-items: center;
	gap: 20px;
}

.dealerImportButton {
	@apply --button;
	margin: 0;
	min-width: 200px;

	&:disabled {
		cursor: not-allowed;
		opacity: 0.8;
	}

	&.inProgress {
		background-color: var(--primary-color) !important;
		color: var(--white) !important;
		animation: inProgress 1s infinite linear;
	}

	&.success {
		background-color: var(--success-color) !important;
		color: var(--white) !important;
	}

	&.failed {
		background-color: var(--error-color) !important;
		color: var(--white) !important;
	}

	&.download {
		background-color: var(--primary-color) !important;
		color: var(--white) !important;
		min-width: 50px;
		width: 50px;
		height: 50px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0;
		font-size: 20px;
		font-weight: bold;
	}
}

.dealerImportInfo {
	display: flex;
	flex-direction: column;
	gap: 8px;
	flex: 1;

	& .infoItem {
		font-size: var(--caption-font-size);
		color: var(--black);
		font-family: var(--font-text);
	}
}

@keyframes pulse {
	0% {
		opacity: 1;
		transform: scale(1);
	}

	50% {
		opacity: 0.5;
		transform: scale(1.2);
	}

	100% {
		opacity: 1;
		transform: scale(1);
	}
}

@keyframes buttonPulse {
	0% {
		box-shadow: 0 0 0 0 rgba(0, 153, 218, 0.7);
	}

	70% {
		box-shadow: 0 0 0 10px rgba(0, 153, 218, 0);
	}

	100% {
		box-shadow: 0 0 0 0 rgba(0, 153, 218, 0);
	}
}

.dealerImportStatusInProgress {
	animation: inProgress 1s infinite linear;
	color: var(--blue);
}

.dealerImportStatusSuccess {
	background-color: #2ecc71;
	color: white;
}

.dealerImportStatusFailed {
	background-color: #e74c3c;
	color: white;
}

@keyframes inProgress {
	0% {
		opacity: 1;
	}

	50% {
		opacity: 0.4;
	}

	100% {
		opacity: 1;
	}
}