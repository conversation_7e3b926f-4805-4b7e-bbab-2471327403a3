/* @flow */
import React from 'react';
import { Link } from 'react-router-dom';
import { Map, List } from 'immutable';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import type { ImmutableIdentity } from '../../../../modules/request/types';
import Brand<PERSON><PERSON> from '../../modules/BrandLogo';
import { CONFIG } from '../DealerConfig/constants';
import CheckMark from '../../modules/CheckMark';

import style from './dealerItem.css';

type Props = {
	identity: ImmutableIdentity,
	data: Map<any, any>,
	dealerLabel: string,
	dealerGroupLabels: List<string>,
};

function addProtocol(url: string): string {
	if (url && url.indexOf('http') !== 0) {
		return `https://${url}`;
	}
	return url;
}

function DealerItem({ identity, data, dealerLabel, dealerGroupLabels = new List() }: Props) {
	if (!identity) return null;

	const locations = data.getIn(['imported', 'location-details']);

	return (
		<tr>
			<td>
				<CheckMark value={data.getIn(['imported', 'active'], false)} />
			</td>
			<td>
				<Link
					className={style.edit}
					to={`/dealers/${identity.get(0)}`}
				>
					{identity.get(0)}
				</Link>
			</td>
			<td>
				{dealerLabel}
				{dealerGroupLabels.sort().map(label => (
					<Link
						className={style.dealerGroupLabel}
						to={`/dealerGroups/${label}`}
						key={label}
					>
						{label}
					</Link>
				))}
			</td>
			<td>{data.getIn(['imported', 'contact-info', 'address', 'name1'])}</td>
			<td>{data.getIn(['imported', 'contact-info', 'address', 'name2'])}</td>
			<td>
				{data.getIn(['imported', 'contact-info', 'address', 'short-name'])}<br />
				{locations && locations.size > 1 && (
					<small><i>{locations.size} locations</i></small>
				)}
			</td>
			{CONFIG.brands.map(brand => (
				<td
					key={brand.id}
					className={style.noPadding}
				>
					{data.getIn(['imported', 'brands', brand.id]) && data.getIn(['imported', 'brands', brand.id], new Map()).size > 0 &&
						<BrandLogo
							shortname={brand.id}
						/>
					}
				</td>
			))}
			<td className={style.www}>
				{locations && (
					<ul>
						{locations.map((location, index) => (
							(location.get('www') && (
								<li key={index}>
									{`${location.getIn(['contact-info', 'language'])}: `}
									<a href={addProtocol(location.get('www'))} target="_blank" rel="noopener noreferrer">{location.get('www')}</a>
								</li>
							)) || null
						))}
					</ul>
				)}
			</td>
		</tr>
	);
}

export default withStyles(style)(DealerItem);
