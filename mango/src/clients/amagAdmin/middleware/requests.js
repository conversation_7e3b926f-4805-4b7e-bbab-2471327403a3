/* @flow */
import { fromJS } from 'immutable';
import config from '../../../tango/config';
import * as request from '../../../modules/request';
import { methods, handleFetch } from '../../../modules/request/request';
import { find } from '../../../modules/article/requests';
import { eqFilter } from '../../../modules/post/tags';
import { TEMPLATE_TYPES } from '../../amagCore/modules/article/constants';
import { SEARCHMODE_SKINNY } from '../../../modules/posts/constants';
import { deactivateAction, createAction } from '../../../modules/alias/requests';
import { type CreateArgs } from '../../../modules/alias/types';
import { WAIT_FOR_ALL } from '../../../modules/request/command';
import { type Aref } from '../../../modules/request/types';

const headers = {
	Accept: 'application/json',
	'Content-Type': 'application/json',
};

async function dispatch(spectraAuthToken: ?string, body: string): Promise<any> {
	const url = `${config.black.url}${config.black.apiPath}/amag/dispatcher`;
	const actualHeaders = spectraAuthToken ? { ...headers, 'spectra-auth-token': spectraAuthToken } : { ...headers };
	return handleFetch(fetch(url, {
		method: methods.POST,
		headers: actualHeaders,
		body: JSON.stringify(body),
	}));
}

async function resolveDealerId(context: Object, dealerId: string): any {
	const queryName = 'qry.dealer.resolve-id';
	const action = request.createAction(queryName, 0, { 'customer-nr': dealerId });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	const data = request.getQueryData(queryName, res);
	return data ? data['dealer-id'] : undefined;
}

async function findAllDealerBrandWebsites(context: Object): any {
	const queryName = 'qry.dealer-brand-website.find-all';
	const action = request.createAction(queryName, 0);
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return {
		hits: request.getQueryData(queryName, res),
		total: request.getQueryData(queryName, res).length,
	};
}

async function findAllDealerWebsites(context: Object): any {
	const queryName = 'qry.dealer-website.find-all';
	const action = request.createAction(queryName, 0);
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return {
		hits: request.getQueryData(queryName, res),
		total: request.getQueryData(queryName, res).length,
	};
}

async function findAllTeamMembers(context: Object): any {
	const findResult = await find(context);
	const posts = fromJS(findResult.hits);

	return posts.filter((post) => {
		const type = post.getIn(['post', 'type']);
		const template = post.getIn(['post', 'template']);

		return type === 'article' && template === TEMPLATE_TYPES.ARTICLE.AMAG_TEAMMEMBER;
	});
}

async function findAllPostsForDealer(dealerId: string): any {
	const options = {
		searchMode: SEARCHMODE_SKINNY,
		size: 1000,
		filter: [eqFilter(dealerId, 'dealer')],
		interestContext: ['amagMigrationRootCtx', 'dashboard'],
	};

	const queryName = 'qry.post.find';
	const action = request.createAction(queryName, 0, options);
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await dispatch(undefined, req);
	const findResult = request.getQueryData(queryName, res);
	const posts = fromJS(findResult.hits);
	return posts;
}

async function deactivateAlias(spectraAuthToken: string, aref: Aref): any {
	const cmdAction = deactivateAction();
	const command = request.createCommand([cmdAction], aref, [WAIT_FOR_ALL], false, true);
	const req = request.create({ command });
	const res = await dispatch(spectraAuthToken, req);
	return request.getRootAref(res);
}

async function runDealerImport(spectraAuthToken: string): any {
	const action = request.createAction('cmd.dealer-import.run', 0, {});
	const command = request.createCommand([action], null, [WAIT_FOR_ALL], false, true);
	const req = request.create({ command });
	const res = await dispatch(spectraAuthToken, req);
	return request.getRootAref(res);
}

async function getDealerImport(spectraAuthToken: string): any {
	const queryName = 'qry.dealer-import.get';
	const action = request.createAction(queryName, 0, {});
	const query = request.createQuery([action], null, [WAIT_FOR_ALL], false, true);
	const req = request.create({ query });
	const res = await dispatch(spectraAuthToken, req);
	return request.getQueryData(queryName, res);
}

async function downloadDealerImportXml(spectraAuthToken: string): any {
	const queryName = 'qry.dealer-import.download';
	const action = request.createAction(queryName, 0, {});
	const query = request.createQuery([action], null, [WAIT_FOR_ALL], false, true);
	const req = request.create({ query });
	const res = await dispatch(spectraAuthToken, req);
	return request.getQueryData(queryName, res);
}

async function createAlias(
	spectraAuthToken: string,
	data: CreateArgs,
): any {
	const cmdAction = createAction(data);
	const command = request.createCommand([cmdAction], null, [WAIT_FOR_ALL], false, true);
	const req = request.create({ command });
	const res = await dispatch(spectraAuthToken, req);
	return request.getRootAref(res);
}

export {
	dispatch,
	resolveDealerId,
	findAllDealerBrandWebsites,
	findAllDealerWebsites,
	findAllTeamMembers,
	findAllPostsForDealer,
	deactivateAlias,
	createAlias,
	runDealerImport,
	getDealerImport,
	downloadDealerImportXml,
};
