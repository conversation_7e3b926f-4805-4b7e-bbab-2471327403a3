/* @flow */
import React from 'react';

import logoVW from '../../assets/img/logos/vw.svg';
import logoAudi from '../../assets/img/logos/audi.svg';
import logoSeat from '../../assets/img/logos/seat.svg';
import logoSkoda from '../../assets/img/logos/skoda.svg';
import logoCupra from '../../assets/img/logos/cupra.svg';
import logoVWNF from '../../assets/img/logos/vwnf.svg';

type Props = {
	shortname: string,
}

function getLogo(shortname): string {
	switch (shortname) {
		case 'volkswagen':
			return logoVW;
		case 'audi':
			return logoAudi;
		case 'seat':
			return logoSeat;
		case 'skoda':
			return logoSkoda;
		case 'cupra':
			return logoCupra;
		case 'volkswagen-nutzfahrzeuge':
			return logoVWNF;
		default:
			return '';
	}
}

function BrandLogo({ shortname }: Props) {
	return <img src={getLogo(shortname)} alt={shortname} title={shortname.toUpperCase()} />;
}

export default (BrandLogo);
