/* @flow */
import { Observable } from 'rxjs/Observable';
import 'rxjs/add/observable/fromPromise';
import 'rxjs/add/observable/of';
import 'rxjs/add/operator/do';
import 'rxjs/add/operator/map';
import 'rxjs/add/operator/catch';
import 'rxjs/add/operator/mergeMap';
import {
	ActionsObservable as ActObs,
	StateObservable,
	combineEpics,
} from 'redux-observable';
import { fromJS } from 'immutable';
import * as constants from './constants';
import {
	find,
	get,
	setDealerConfig,
	initializeDealerLabel,
	setDealerWebsiteActive,
	setDealerWebsiteConfig,
	setAmagRetailerFlag,
	setDealerFlags,
	initializeBrandWebsite,
	setBrandWebsiteActive,
	setBrandWebsiteConfig,
	setDealerGroupLabels,
	findAllDealerGroups,
	createDealerGroup,
	activateDealerGroupWebsite,
	deactivateDealerGroupWebsite,
	findDealerGroupByLabel,
	setDealerGroupAmagFlag,
	setDealerGroupWebsiteConfig,
	findDealerGroupByLabelWithDealers,
	setDealerConfigInternal,
	getDealerConfigInternal,
} from './requests';
import {
	findSucceeded,
	getSucceeded,
	reloadRequested,
	setDealerConfigSucceeded,
	setAmagRetailerFlagSucceeded,
	setDealerFlagsSucceeded,
	setDealerWebsiteConfigSucceeded,
	setBrandWebsiteConfigSucceeded,
	setDealerWebsiteActiveSucceeded,
	setBrandWebsiteActiveSucceeded,
	createDraft,
	setDealerGroupLabelsSucceeded,
	getAllDealerGroupsSucceeded,
	createDealerGroupSucceeded,
	activateDealerGroupSucceeded,
	deactivateDealerGroupSucceeded,
	findDealerGroupByLabelSucceeded,
	setDealerGroupAmagFlagSucceeded,
	saveDealerGroupConfigSucceeded,
	findDealerGroupByLabelWithDealersSucceeded,
	setDealerConfigInternalSucceeded,
	getDealerConfigInternalSucceeded,
	getDealerImport as getDealerImportAction,
	getDealerConfigInternal as getDealerConfigInternalAction, getDealerImportSucceeded,
	downloadDealerImportXmlSucceeded,
} from './actions';

import {
	requestStarted,
	requestSucceeded,
	requestFailed,
} from '../../../../modules/request/actions';
import { getRequestContext } from '../../../../modules/config';
import { getDealerImport, runDealerImport, downloadDealerImportXml } from '../../middleware/requests';

function findEpic(action$: ActObs<*>, state$: StateObservable<any>): ActObs<*> {
	const requestId = 'findDealers';
	return action$.ofType(constants.FIND_STARTED).mergeMap(action => Observable.concat(
			Observable.of(requestStarted(requestId)),
			Observable.fromPromise(
				find(getRequestContext(state$.value), action.payload),
			)
				.flatMap(payload => [
					findSucceeded(fromJS(payload)),
					requestSucceeded(requestId),
				])
				.catch(error => Observable.of(requestFailed(requestId, error))),
		));
}

function getEpic(action$: ActObs<*>, state$: StateObservable<any>): ActObs<*> {
	const requestId = 'getDealer';
	return action$
		.ofType(constants.GET_STARTED, constants.RELOAD_REQUESTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					get(getRequestContext(state$.value), action.payload),
				)
					.flatMap(payload => [
						getSucceeded(fromJS(payload)),
						requestSucceeded(requestId),
						createDraft(),
					])
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function getDealerConfigInternalEpic(action$: ActObs<*>, state$: StateObservable<any>): ActObs<*> {
	const requestId = 'getDealerConfigInternal';
	return action$
		.ofType(constants.GET_DEALER_CONFIG_INTERNAL_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					getDealerConfigInternal(getRequestContext(state$.value), action.payload),
				)
					.flatMap(payload => [
						getDealerConfigInternalSucceeded(fromJS(payload)),
						requestSucceeded(requestId),
					])
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function getDealerImportEpic(action$: ActObs<*>, state$: StateObservable<any>): ActObs<*> {
	const requestId = 'getDealerImport';
	return action$
		.ofType(constants.GET_DEALER_IMPORT_STARTED)
		.mergeMap(_action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					getDealerImport(getRequestContext(state$.value)),
				)
					.flatMap(payload => Observable.concat(
						Observable.of(getDealerImportSucceeded(fromJS(payload))),
						Observable.of(requestSucceeded(requestId)),
					))
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function runDealerImportEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'runDealerImport';
	return action$
		.ofType(constants.RUN_DEALER_IMPORT_STARTED)
		.mergeMap(_action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					runDealerImport(
						getRequestContext(state$.value),
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(requestSucceeded(requestId)),
							Observable.of(getDealerImportAction()),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function downloadDealerImportXmlEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'downloadDealerImportXml';
	return action$
		.ofType(constants.DOWNLOAD_DEALER_IMPORT_XML_STARTED)
		.mergeMap(_action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					downloadDealerImportXml(
						getRequestContext(state$.value),
					),
				)
					.flatMap(payload => {
						// Create a blob and trigger download
						const blob = new Blob([payload.content], { type: 'application/xml' });
						const url = window.URL.createObjectURL(blob);
						const a = document.createElement('a');
						a.style.display = 'none';
						a.href = url;
						a.download = payload.filename || 'dealer-import.xml';
						document.body.appendChild(a);
						a.click();
						window.URL.revokeObjectURL(url);
						document.body.removeChild(a);

						return Observable.concat(
							Observable.of(downloadDealerImportXmlSucceeded(payload)),
							Observable.of(requestSucceeded(requestId)),
						);
					})
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}


function initializeDealerLabelEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'initializeDealerLabel';
	return action$
		.ofType(constants.INITIALIZE_DEALER_LABEL_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					initializeDealerLabel(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.dealerLabel,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(requestSucceeded(requestId)),
							Observable.of(reloadRequested(action.payload.identity)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function setDealerWebsiteActiveEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerWebsiteConfig';
	return action$
		.ofType(constants.SET_DEALER_WEBSITE_ACTIVE_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					setDealerWebsiteActive(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.active,
					),
				)
					.flatMap(payload =>
						Observable.concat(
							// eslint-disable-next-line max-len
							Observable.of(
								setDealerWebsiteActiveSucceeded(
									fromJS(payload.identity),
									payload.active,
								),
							),
							Observable.of(requestSucceeded(requestId)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function initializeBrandWebsiteEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'brandWebsite.initialize';
	return action$
		.ofType(constants.INITIALIZE_BRAND_WEBSITE_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					initializeBrandWebsite(
						getRequestContext(state$.value),
						action.payload.dealerId,
						action.payload.dealerLabel,
						action.payload.brand,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(requestSucceeded(requestId)),
							Observable.of(
								reloadRequested(fromJS([action.payload.dealerId, 'dealer'])),
							),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function setBrandWebsiteActiveEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.SET_BRAND_WEBSITE_ACTIVE_STARTED)
		.mergeMap((action) => {
			const requestId = `setDealerBrandWebsiteConfig.${
				action.payload.identity[0]
			}`;
			return Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					setBrandWebsiteActive(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.active,
					),
				)
					.flatMap(payload =>
						Observable.concat(
							// eslint-disable-next-line max-len
							Observable.of(
								setBrandWebsiteActiveSucceeded(
									fromJS(payload.identity),
									payload.active,
									action.payload.brand,
								),
							),
							Observable.of(requestSucceeded(requestId)),
							Observable.of(
								reloadRequested(fromJS([action.payload.dealerId, 'dealer'])),
							),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			);
		});
}

function setDealerConfigEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerConfig';
	return action$.ofType(constants.SET_DEALER_CONFIG_STARTED).mergeMap(action =>
		Observable.concat(
			Observable.of(requestStarted(requestId)),
			Observable.fromPromise(
				// eslint-disable-next-line max-len
				setDealerConfig(
					getRequestContext(state$.value),
					action.payload.identity,
					action.payload.config,
				),
			)
				.flatMap(() =>
					Observable.concat(
						Observable.of(setDealerConfigSucceeded()),
						Observable.of(requestSucceeded(requestId)),
					),
				)
				.catch(error => Observable.of(requestFailed(requestId, error))),
		),
	);
}

function setDealerConfigInternalEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerConfigInternal';
	return action$.ofType(constants.SET_DEALER_CONFIG_INTERNAL_STARTED).mergeMap(action =>
		Observable.concat(
			Observable.of(requestStarted(requestId)),
			Observable.fromPromise(
				// eslint-disable-next-line max-len
				setDealerConfigInternal(
					getRequestContext(state$.value),
					action.payload.identity,
					action.payload.config,
				),
			)
				.flatMap(() =>
					Observable.concat(
						Observable.of(setDealerConfigInternalSucceeded()),
						Observable.of(requestSucceeded(requestId)),
						Observable.of(getDealerConfigInternalAction(action.payload.identity)),
					),
				)
				.catch(error => Observable.of(requestFailed(requestId, error))),
		),
	);
}

function setAmagRetailerFlagEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setAmagRetailerFlag';
	return action$
		.ofType(constants.SET_AMAG_RETAILER_FLAG_STARTED)
		.mergeMap((action) => {
			const { identity, isAmagRetailer } = action.payload;
			return Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					// eslint-disable-next-line max-len
					setAmagRetailerFlag(
						getRequestContext(state$.value),
						identity,
						isAmagRetailer,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								setAmagRetailerFlagSucceeded(identity, isAmagRetailer),
							),
							Observable.of(requestSucceeded(requestId)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			);
		});
}

function setDealerFlagsEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerFlags';
	return action$.ofType(constants.SET_DEALER_FLAGS_STARTED).mergeMap((action) => {
		const { identity, flags } = action.payload;
		return Observable.concat(
			Observable.of(requestStarted(requestId)),
			Observable.fromPromise(
				setDealerFlags(getRequestContext(state$.value), identity, flags),
			)
				.flatMap(() =>
					Observable.concat(
						Observable.of(setDealerFlagsSucceeded(identity, flags)),
						Observable.of(requestSucceeded(requestId)),
					),
				)
				.catch(error => Observable.of(requestFailed(requestId, error))),
		);
	});
}

function setDealerWebsiteConfigEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerWebsiteConfig';
	return action$
		.ofType(constants.SET_DEALER_WEBSITE_CONFIG_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(requestId)),
				// eslint-disable-next-line max-len
				Observable.fromPromise(
					setDealerWebsiteConfig(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.config,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(setDealerWebsiteConfigSucceeded()),
							Observable.of(requestSucceeded(requestId)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			),
		);
}

function setBrandWebsiteConfigEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.SET_BRAND_WEBSITE_CONFIG_STARTED)
		.mergeMap((action) => {
			const requestId = `setDealerBrandWebsiteConfig.${
				action.payload.identity[0]
			}`;
			return Observable.concat(
				Observable.of(requestStarted(requestId)),
				// eslint-disable-next-line max-len
				Observable.fromPromise(
					setBrandWebsiteConfig(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.config,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								setBrandWebsiteConfigSucceeded(action.payload.brand),
							),
							Observable.of(requestSucceeded(requestId)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			);
		});
}

function setDealerGroupLabelsEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	const requestId = 'setDealerGroupLabels';
	return action$
		.ofType(constants.SET_DEALER_GROUP_LABELS_STARTED)
		.mergeMap((action) => {
			const { identity, labels } = action.payload;
			return Observable.concat(
				Observable.of(requestStarted(requestId)),
				Observable.fromPromise(
					setDealerGroupLabels(
						getRequestContext(state$.value),
						identity,
						labels,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(setDealerGroupLabelsSucceeded(identity, labels)),
							Observable.of(requestSucceeded(requestId)),
						),
					)
					.catch(error => Observable.of(requestFailed(requestId, error))),
			);
		});
}

function getAllDealerGroupLabelsEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$.ofType(constants.GET_ALL_DEALER_GROUPS_STARTED).mergeMap(() =>
		Observable.concat(
			Observable.of(requestStarted(constants.GET_ALL_DEALER_GROUPS_REQUESTID)),
			Observable.fromPromise(
				findAllDealerGroups(getRequestContext(state$.value)),
			)
				.flatMap(payload =>
					Observable.concat(
						Observable.of(getAllDealerGroupsSucceeded(payload.data)),
						Observable.of(
							requestSucceeded(constants.GET_ALL_DEALER_GROUPS_REQUESTID),
						),
					),
				)
				.catch(error =>
					Observable.of(
						requestFailed(constants.GET_ALL_DEALER_GROUPS_REQUESTID, error),
					),
				),
		),
	);
}

function createDealerGroupEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.CREATE_DEALER_GROUP_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(requestStarted(constants.CREATE_DEALER_GROUP_REQUESTID)),
				Observable.fromPromise(
					createDealerGroup(
						getRequestContext(state$.value),
						action.payload.label,
						action.payload.name,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								createDealerGroupSucceeded(
									action.payload.label,
									action.payload.label,
								),
							),
							Observable.of(
								requestSucceeded(constants.CREATE_DEALER_GROUP_REQUESTID),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(constants.CREATE_DEALER_GROUP_REQUESTID, error),
						),
					),
			),
		);
}

function activateDealerGroupEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.ACTIVATE_DEALER_GROUP_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(constants.ACTIVATE_DEALER_GROUP_REQUESTID),
				),
				Observable.fromPromise(
					activateDealerGroupWebsite(
						getRequestContext(state$.value),
						action.payload.identity,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								activateDealerGroupSucceeded(action.payload.identity),
							),
							Observable.of(
								requestSucceeded(constants.ACTIVATE_DEALER_GROUP_REQUESTID),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(constants.ACTIVATE_DEALER_GROUP_REQUESTID, error),
						),
					),
			),
		);
}

function deactivateDealerGroupEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.DEACTIVATE_DEALER_GROUP_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(constants.DEACTIVATE_DEALER_GROUP_REQUESTID),
				),
				Observable.fromPromise(
					deactivateDealerGroupWebsite(
						getRequestContext(state$.value),
						action.payload.identity,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								deactivateDealerGroupSucceeded(action.payload.identity),
							),
							Observable.of(
								requestSucceeded(constants.DEACTIVATE_DEALER_GROUP_REQUESTID),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(constants.DEACTIVATE_DEALER_GROUP_REQUESTID, error),
						),
					),
			),
		);
}

function getDealerGroupByLabelEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.FIND_DEALER_GROUP_BY_LABEL_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(constants.FIND_DEALER_GROUP_BY_LABEL_REQUESTID),
				),
				Observable.fromPromise(
					findDealerGroupByLabel(
						getRequestContext(state$.value),
						action.payload.label,
					),
				)
					.flatMap(payload =>
						Observable.concat(
							Observable.of(findDealerGroupByLabelSucceeded(payload)),
							Observable.of(
								requestSucceeded(
									constants.FIND_DEALER_GROUP_BY_LABEL_REQUESTID,
								),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(
								constants.FIND_DEALER_GROUP_BY_LABEL_REQUESTID,
								error,
							),
						),
					),
			),
		);
}

function getDealerGroupByLabelWithDealersEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(
						constants.FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_REQUESTID,
					),
				),
				Observable.fromPromise(
					findDealerGroupByLabelWithDealers(
						getRequestContext(state$.value),
						action.payload.label,
					),
				)
					.flatMap(payload =>
						Observable.concat(
							Observable.of(
								findDealerGroupByLabelWithDealersSucceeded(payload),
							),
							Observable.of(
								requestSucceeded(
									constants.FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_REQUESTID,
								),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(
								constants.FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_REQUESTID,
								error,
							),
						),
					),
			),
		);
}

function setDealerGroupAmagFlagEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.SET_DEALER_GROUP_AMAG_FLAG_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(constants.SET_DEALER_GROUP_AMAG_FLAG_REQUESTID),
				),
				Observable.fromPromise(
					setDealerGroupAmagFlag(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.flag,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								setDealerGroupAmagFlagSucceeded(
									action.payload.identity,
									action.payload.flag,
								),
							),
							Observable.of(
								requestSucceeded(
									constants.SET_DEALER_GROUP_AMAG_FLAG_REQUESTID,
								),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(
								constants.SET_DEALER_GROUP_AMAG_FLAG_REQUESTID,
								error,
							),
						),
					),
			),
		);
}

function saveDealerGroupConfigEpic(
	action$: ActObs<*>,
	state$: StateObservable<any>,
): ActObs<*> {
	return action$
		.ofType(constants.SAVE_DEALER_GROUP_CONFIG_STARTED)
		.mergeMap(action =>
			Observable.concat(
				Observable.of(
					requestStarted(constants.SAVE_DEALER_GROUP_CONFIG_REQUESTID),
				),
				Observable.fromPromise(
					setDealerGroupWebsiteConfig(
						getRequestContext(state$.value),
						action.payload.identity,
						action.payload.config,
					),
				)
					.flatMap(() =>
						Observable.concat(
							Observable.of(
								saveDealerGroupConfigSucceeded(
									action.payload.identity,
									action.payload.config,
								),
							),
							Observable.of(
								requestSucceeded(constants.SAVE_DEALER_GROUP_CONFIG_REQUESTID),
							),
						),
					)
					.catch(error =>
						Observable.of(
							requestFailed(
								constants.SAVE_DEALER_GROUP_CONFIG_REQUESTID,
								error,
							),
						),
					),
			),
		);
}

const epic = combineEpics(
	findEpic,
	getEpic,
	getDealerConfigInternalEpic,

	getDealerImportEpic,
	runDealerImportEpic,
	downloadDealerImportXmlEpic,

	setDealerConfigEpic,
	setAmagRetailerFlagEpic,
	setDealerFlagsEpic,
	initializeDealerLabelEpic,
	setDealerWebsiteActiveEpic,
	setDealerWebsiteConfigEpic,
	setDealerConfigInternalEpic,

	initializeBrandWebsiteEpic,
	setBrandWebsiteActiveEpic,
	setBrandWebsiteConfigEpic,
	setDealerGroupLabelsEpic,
	getAllDealerGroupLabelsEpic,
	createDealerGroupEpic,
	activateDealerGroupEpic,
	deactivateDealerGroupEpic,
	getDealerGroupByLabelEpic,
	getDealerGroupByLabelWithDealersEpic,
	setDealerGroupAmagFlagEpic,
	saveDealerGroupConfigEpic,
);

export default epic;
