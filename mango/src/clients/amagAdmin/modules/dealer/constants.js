/* @flow */
export const FIND_STARTED = 'dealer/FIND_STARTED';
export const FIND_SUCCEEDED = 'dealer/FIND_SUCCEEDED';

export const GET_DEALER_IMPORT_STARTED = 'dealer/GET_DEALER_IMPORT_STARTED';
export const GET_DEALER_IMPORT_SUCCEEDED = 'dealer/GET_DEALER_IMPORT_SUCCEEDED';

export const RUN_DEALER_IMPORT_STARTED = 'dealer/RUN_DEALER_IMPORT_STARTED';
export const RUN_DEALER_IMPORT_SUCCEEDED = 'dealer/RUN_DEALER_IMPORT_SUCCEEDED';

export const DOWNLOAD_DEALER_IMPORT_XML_STARTED = 'dealer/DOWNLOAD_DEALER_IMPORT_XML_STARTED';
export const DOWNLOAD_DEALER_IMPORT_XML_SUCCEEDED = 'dealer/DOWNLOAD_DEALER_IMPORT_XML_SUCCEEDED';

export const GET_STARTED = 'dealer/GET_STARTED';
export const GET_SUCCEEDED = 'dealer/GET_SUCCEEDED';

export const GET_DEALER_CONFIG_INTERNAL_STARTED = 'dealer/GET_DEALER_CONFIG_INTERNAL_STARTED';
export const GET_DEALER_CONFIG_INTERNAL_SUCCEEDED = 'dealer/GET_DEALER_CONFIG_INTERNAL_SUCCEEDED';

export const RELOAD_REQUESTED = 'dealer/RELOAD_REQUESTED';

export const INITIALIZE_DEALER_LABEL_STARTED = 'dealer/INITIALIZE_DEALER_LABEL_STARTED';
export const SET_DEALER_CONFIG_STARTED = 'dealer/SET_DEALER_CONFIG_STARTED';
export const SET_DEALER_CONFIG_SUCCEEDED = 'dealer/SET_DEALER_CONFIG_SUCCEEDED';

export const SET_AMAG_RETAILER_FLAG_STARTED = 'dealer/SET_AMAG_RETAILER_FLAG_STARTED';
export const SET_AMAG_RETAILER_FLAG_SUCCEEDED = 'dealer/SET_AMAG_RETAILER_FLAG_SUCCEEDED';

export const SET_DEALER_FLAGS_STARTED = 'dealer/SET_DEALER_FLAGS_STARTED';
export const SET_DEALER_FLAGS_SUCCEEDED = 'dealer/SET_DEALER_FLAGS_SUCCEEDED';

export const SET_DEALER_WEBSITE_ACTIVE_STARTED = 'dealer/SET_DEALER_WEBSITE_ACTIVE_STARTED';
export const SET_DEALER_WEBSITE_ACTIVE_SUCCEEDED = 'dealer/SET_DEALER_WEBSITE_ACTIVE_SUCCEEDED';

export const SET_DEALER_WEBSITE_CONFIG_STARTED = 'dealer/SET_DEALER_WEBSITE_CONFIG_STARTED';
export const SET_DEALER_WEBSITE_CONFIG_SUCCEEDED = 'dealer/SET_DEALER_WEBSITE_CONFIG_SUCCEEDED';

export const INITIALIZE_BRAND_WEBSITE_STARTED = 'dealer/INITIALIZE_BRAND_WEBSITE_STARTED';

export const SET_BRAND_WEBSITE_CONFIG_STARTED = 'dealer/SET_BRAND_WEBSITE_CONFIG_STARTED';
export const SET_BRAND_WEBSITE_CONFIG_SUCCEEDED = 'dealer/SET_BRAND_WEBSITE_CONFIG_SUCCEEDED';

export const SET_BRAND_WEBSITE_ACTIVE_STARTED = 'dealer/SET_BRAND_WEBSITE_ACTIVE_STARTED';
export const SET_BRAND_WEBSITE_ACTIVE_SUCCEEDED = 'dealer/SET_BRAND_WEBSITE_ACTIVE_SUCCEEDED';

export const SET_DEALER_CONFIG_INTERNAL_STARTED = 'dealer/SET_DEALER_CONFIG_INTERNAL_STARTED';
export const SET_DEALER_CONFIG_INTERNAL_SUCCEEDED = 'dealer/SET_DEALER_CONFIG_INTERNAL_SUCCEEDED';

export const DRAFT_CREATED = 'dealer/DRAFT_CREATED';
export const DEALER_DRAFT_UPDATED = 'dealer/DEALER_DRAFT_UPDATED';
export const DEALER_DRAFT_LOCATION_UPDATED = 'dealer/DEALER_DRAFT_LOCATION_UPDATED';
export const DEALER_DRAFT_LOCATION_DELETED = 'dealer/DEALER_DRAFT_LOCATION_DELETED';
export const DEALER_DRAFT_LOCATION_ADDED = 'dealer/DEALER_DRAFT_LOCATION_ADDED';
export const DEALER_DRAFT_LOCATION_DEPARTMENT_DELETED = 'dealer/DEALER_DRAFT_LOCATION_DEPARTMENT_DELETED';
export const DEALER_DRAFT_LOCATION_DEPARTMENT_ADDED = 'dealer/DEALER_DRAFT_LOCATION_DEPARTMENT_ADDED';
export const DEALER_DRAFT_LOCATION_RESET = 'dealer/DEALER_DRAFT_LOCATION_RESET';

export const DEALER_WEBSITE_DRAFT_UPDATED = 'dealer/DEALER_WEBSITE_DRAFT_UPDATED';
export const DEALER_BRAND_WEBSITE_DRAFT_UPDATED = 'dealer/DEALER_BRAND_WEBSITE_DRAFT_UPDATED';

export const SET_DEALER_GROUP_LABELS_STARTED = 'dealer/SET_DEALER_GROUP_LABELS_STARTED';
export const SET_DEALER_GROUP_LABELS_SUCCEEDED = 'dealer/SET_DEALER_GROUP_LABELS_SUCCEEDED';

export const GET_ALL_DEALER_GROUPS_STARTED = 'dealer/GET_ALL_DEALER_GROUPS_STARTED';
export const GET_ALL_DEALER_GROUPS_SUCCEEDED = 'dealer/GET_ALL_DEALER_GROUPS_SUCCEEDED';
export const GET_ALL_DEALER_GROUPS_REQUESTID = 'getAllDealerGroups';

export const CREATE_DEALER_GROUP_STARTED = 'dealer/CREATE_DEALER_GROUP_STARTED';
export const CREATE_DEALER_GROUP_SUCCEEDED = 'dealer/CREATE_DEALER_GROUP_SUCCEEDED';
export const CREATE_DEALER_GROUP_REQUESTID = 'createDealerGroup';

export const ACTIVATE_DEALER_GROUP_STARTED = 'dealer/ACTIVATE_DEALER_GROUP_STARTED';
export const ACTIVATE_DEALER_GROUP_SUCCEEDED = 'dealer/ACTIVATE_DEALER_GROUP_SUCCEEDED';
export const ACTIVATE_DEALER_GROUP_REQUESTID = 'activateDealerGroup';

export const DEACTIVATE_DEALER_GROUP_STARTED = 'dealer/DEACTIVATE_DEALER_GROUP_STARTED';
export const DEACTIVATE_DEALER_GROUP_SUCCEEDED = 'dealer/DEACTIVATE_DEALER_GROUP_SUCCEEDED';
export const DEACTIVATE_DEALER_GROUP_REQUESTID = 'deactivateDealerGroup';

export const FIND_DEALER_GROUP_BY_LABEL_STARTED = 'dealer/FIND_DEALER_GROUP_BY_LABEL_STARTED';
export const FIND_DEALER_GROUP_BY_LABEL_SUCCEEDED = 'dealer/FIND_DEALER_GROUP_BY_LABEL_SUCCEEDED';
export const FIND_DEALER_GROUP_BY_LABEL_REQUESTID = 'findGroupByLabel';

export const FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_STARTED = 'dealer/FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_STARTED';
export const FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_SUCCEEDED = 'dealer/FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_SUCCEEDED';
export const FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_REQUESTID = 'findGroupByLabelWithDealers';

export const SET_DEALER_GROUP_AMAG_FLAG_STARTED = 'dealer/SET_DEALER_GROUP_AMAG_FLAG_STARTED';
export const SET_DEALER_GROUP_AMAG_FLAG_SUCCEEDED = 'dealer/SET_DEALER_GROUP_AMAG_FLAG_SUCCEEDED';
export const SET_DEALER_GROUP_AMAG_FLAG_REQUESTID = 'setAmagFlag';

export const SET_DEALER_GROUP_CONFIG = 'dealer/SET_DEALER_GROUP_CONFIG';
export const SAVE_DEALER_GROUP_CONFIG_SUCCEEDED = 'dealer/SAVE_DEALER_GROUP_CONFIG_SUCCEEDED';
export const SAVE_DEALER_GROUP_CONFIG_REQUESTID = 'saveDealerGroupConfig';
export const SAVE_DEALER_GROUP_CONFIG_STARTED = 'dealer/SAVE_DEALER_GROUP_CONFIG_STARTED';

export const FLAG_AMAG = 'amag';

export const DAYS = ['mo', 'tu', 'we', 'th', 'fr', 'sa', 'su'];
export const DEPARTMENTS = ['SALES', 'SERVICE', 'PAINT', 'PARTS', 'RENTAL', 'FUEL_STATION', 'EFUEL_STATION', 'CARWASH'];
