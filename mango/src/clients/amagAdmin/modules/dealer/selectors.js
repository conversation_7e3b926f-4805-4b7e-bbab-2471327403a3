/* @flow */
import { createSelector } from 'reselect';
import { List, Map, fromJS } from 'immutable';
import { type Identity } from '../../../../modules/request/types';
import { prepareImportedLocations, prepareMergedLocations } from '../../../amagCore/modules/DealerConfigProvider/prepareLocations';

function getState(state) {
	return state.get('dealer', new Map());
}

const getByIdentity = createSelector(
	getState,
	state => state.get('byIdentity', new List()),
);

const getFind = createSelector(
	getState,
	state => state.get('find', new Map()),
);

const getFindIdentities = createSelector(
	getFind,
	find => find.get('identities', new List()),
);

const getAllDealerGroups = createSelector(
	getState,
	state => state.get('dealerGroups', fromJS([])),
);

const getCurrentDealerGroup = createSelector(
	getState,
	state => state.get('currentDealerGroup', undefined),
);

const getCurrentDealerGroupDealers = createSelector(
	getState,
	state => state.get('currentDealers', undefined),
);

const getDealers = createSelector(
	getFindIdentities,
	getByIdentity,
	(identities, dealers) =>
		identities && dealers && identities.map(identity => dealers.get(identity)),
);

const getDealerImports = createSelector(
	getState,
	state => state.get('import'),
);

const getDealerIds = createSelector(
	getDealers,
	dealers => dealers.map(dealer => dealer.getIn(['dealer', 'identity', 0])),
);

const getCurrentDealerIdentity = createSelector(
	getState,
	state => state.get('current'),
);

const getWholeDealerConfiguration = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity]),
);

const getDealer = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => {
		if (!dealers) {
			return undefined;
		}

		const dealer = dealers.getIn([identity, 'dealer']);
		if (!dealer) {
			return dealer;
		}

		return dealer.updateIn(['config', 'imported', 'location-details'], prepareImportedLocations);
	},
);

const getDealerWebsite = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'dealer-website']),
);

const getDealerBrandWebsites = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'dealer-brand-websites']),
);

const getDealerConfigInternal = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'dealer-config-internal']),
);

const getDealerDraft = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => {
		if (!dealers) {
			return undefined;
		}

		const dealer = dealers.getIn([identity, 'draft', 'dealer']);
		if (!dealer) {
			return dealer;
		}

		const managedLocations = dealers.getIn([identity, 'draft', 'dealer', 'location-details'], new List());

		if (managedLocations.isEmpty()) {
			return dealer;
		}

		return dealer.updateIn(['merged-location-details'], locations => prepareMergedLocations(locations, managedLocations));
	},
);

const getDealerWebsiteDraft = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'draft', 'dealer-website']),
);

const getDealerBrandWebsitesDraft = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'draft', 'dealer-brand-websites']),
);

const getDealerLabel = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'dealer', 'dealer-label']),
);

const getDealerGroupLabels = createSelector(
	getCurrentDealerIdentity,
	getByIdentity,
	(identity, dealers) => dealers && dealers.getIn([identity, 'dealer', 'dealer-group-labels'], new List()),
);

const getBrandWebsites = createSelector(
	getState,
	state => state.getIn(['components', 'brandWebsites']),
);

const getBrandWebsite = (state: State, identity: Identity) => getBrandWebsites(state).get(identity);

const makeGetBrandWebsite = () => createSelector(
	getBrandWebsite,
	state => state,
);

export {
	getDealers,
	getDealerImports,
	getDealerIds,
	getWholeDealerConfiguration,
	getDealer,
	getDealerWebsite,
	getDealerBrandWebsites,
	getDealerConfigInternal,
	getDealerDraft,
	getDealerWebsiteDraft,
	getDealerBrandWebsitesDraft,
	getDealerLabel,
	makeGetBrandWebsite,
	getDealerGroupLabels,
	getAllDealerGroups,
	getCurrentDealerGroup,
	getCurrentDealerGroupDealers,
};
