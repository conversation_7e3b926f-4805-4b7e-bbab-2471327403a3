/* @flow */
import { fromJS } from 'immutable';
import * as constants from './constants';
import * as setBrandWebsiteActive from './reducers/setBrandWebsiteActive';
import * as setDealerWebsiteActive from './reducers/setDealerWebsiteActive';
import * as setAmagRetailerFlag from './reducers/setAmagRetailerFlag';
import * as setDealerFlags from './reducers/setDealerFlags';
import * as findReducers from './reducers/find';
import * as getReducers from './reducers/get';
import * as draftReducers from './reducers/draft';
import * as setDealerGroupLabels from './reducers/setDealerGroupLabels';
import * as getAllDealerGroups from './reducers/getAllDealerGroups';
import * as createDealerGroup from './reducers/createDealerGroup';
import * as getDealerConfigInternalReducers from './reducers/getDealerConfigInternal';
import type {
	FindSucceededAction,
		GetSucceededAction,
		SetAmagRetailerFlagSucceededAction,
		SetDealerFlagsSucceededAction,
		DealerWebsiteActiveSucceededAction,
		DealerBrandWebsiteActiveSucceededAction,
		DealerDraftUpdatedAction,
		DealerDraftLocationUpdatedAction,
		DealerDraftLocationDeletedAction,
		DealerDraftLocationDepartmentDeletedAction,
		DealerDraftLocationAddedAction,
		DealerDraftLocationDepartmentAddedAction,
		DealerDraftLocationResetAction,
		DealerWebsiteDraftUpdatedAction,
		DealerBrandWebsiteDraftUpdatedAction,
		SetDealerBrandWebsiteConfigSucceededAction,
} from './types';


const initialState = fromJS({
	byIdentity: {},
	find: {
		identities: [],
	},
});

function reducer(state: ReduxState = initialState, action: any): ReduxState {
	const { type } = action;
	switch (type) {
		case constants.FIND_SUCCEEDED:
			return findReducers.succeeded(state, (action: FindSucceededAction));
		case constants.GET_STARTED:
			return getReducers.started(state);
		case constants.GET_SUCCEEDED:
			return getReducers.succeeded(state, (action: GetSucceededAction));

		case constants.SET_DEALER_WEBSITE_ACTIVE_SUCCEEDED:
			return setDealerWebsiteActive.succeeded(state, (action: DealerWebsiteActiveSucceededAction));
		case constants.SET_BRAND_WEBSITE_ACTIVE_SUCCEEDED:
			return setBrandWebsiteActive.succeeded(
				state,
				(action: DealerBrandWebsiteActiveSucceededAction),
			);
		case constants.SET_AMAG_RETAILER_FLAG_SUCCEEDED:
			return setAmagRetailerFlag.succeeded(state, (action: SetAmagRetailerFlagSucceededAction));
		case constants.SET_DEALER_FLAGS_SUCCEEDED:
			return setDealerFlags.succeeded(state, (action: SetDealerFlagsSucceededAction));
		case constants.DRAFT_CREATED:
			return draftReducers.createDraft(state);
		case constants.DEALER_DRAFT_UPDATED:
			return draftReducers.updateDealerDraft(state, (action: DealerDraftUpdatedAction));
		case constants.DEALER_DRAFT_LOCATION_UPDATED:
			return draftReducers.updateDealerDraftLocation(
				state, (action: DealerDraftLocationUpdatedAction));
		case constants.DEALER_DRAFT_LOCATION_DELETED:
			return draftReducers.deleteDealerDraftLocation(
				state, (action: DealerDraftLocationDeletedAction));
		case constants.DEALER_DRAFT_LOCATION_DEPARTMENT_DELETED:
			return draftReducers.deleteDealerDraftLocationDepartment(
				state, (action: DealerDraftLocationDepartmentDeletedAction));
		case constants.DEALER_DRAFT_LOCATION_ADDED:
			return draftReducers.addDealerDraftLocation(
				state, (action: DealerDraftLocationAddedAction));
		case constants.DEALER_DRAFT_LOCATION_DEPARTMENT_ADDED:
			return draftReducers.addDealerDraftLocationDepartment(
				state, (action: DealerDraftLocationDepartmentAddedAction));
		case constants.DEALER_DRAFT_LOCATION_RESET:
			return draftReducers.resetDealerDraftLocation(
				state, (action: DealerDraftLocationResetAction));
		case constants.DEALER_WEBSITE_DRAFT_UPDATED:
			return draftReducers.updateDealerWebsiteDraft(
				state, (action: DealerWebsiteDraftUpdatedAction));
		case constants.DEALER_BRAND_WEBSITE_DRAFT_UPDATED:
			return draftReducers.updateDealerBrandWebsiteDraft(
				state, (action: DealerBrandWebsiteDraftUpdatedAction));
		case constants.SET_DEALER_CONFIG_SUCCEEDED:
			return draftReducers.updateDealerDraftSucceded(state);
		case constants.SET_DEALER_WEBSITE_CONFIG_SUCCEEDED:
			return draftReducers.updateDealerWebsiteDraftSucceded(state);
		case constants.SET_BRAND_WEBSITE_CONFIG_SUCCEEDED:
			return draftReducers.updateDealerBrandWebsiteDraftSucceded(
				state, (action: SetDealerBrandWebsiteConfigSucceededAction));
		case constants.SET_DEALER_GROUP_LABELS_SUCCEEDED:
			return setDealerGroupLabels.succeeded(state, action);
		case constants.GET_ALL_DEALER_GROUPS_SUCCEEDED:
			return getAllDealerGroups.succeeded(state, action);
		case constants.CREATE_DEALER_GROUP_SUCCEEDED:
			return createDealerGroup.succeeded(state, action);
		case constants.FIND_DEALER_GROUP_BY_LABEL_SUCCEEDED:
			return state.set('currentDealerGroup', fromJS(action.payload.config));
		case constants.FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_SUCCEEDED:
			return state
				.set('currentDealerGroup', fromJS(action.payload.config.dealerGroup))
				.set('currentDealers', fromJS(action.payload.config.dealers));
		case constants.ACTIVATE_DEALER_GROUP_SUCCEEDED:
			return state.setIn(['currentDealerGroup', 'websiteActive?'], true);
		case constants.DEACTIVATE_DEALER_GROUP_SUCCEEDED:
			return state.setIn(['currentDealerGroup', 'websiteActive?'], false);
		case constants.SET_DEALER_GROUP_AMAG_FLAG_SUCCEEDED:
			return state.setIn(['currentDealerGroup', 'amag?'], action.payload.flag);
		case constants.SET_DEALER_GROUP_CONFIG:
			return state.setIn(['currentDealerGroup', 'websiteConfig'], fromJS(action.payload.config));
		case constants.GET_DEALER_CONFIG_INTERNAL_SUCCEEDED:
			return getDealerConfigInternalReducers.succeeded(state, action);
		case constants.GET_DEALER_IMPORT_SUCCEEDED:
			return state.setIn(['import'], fromJS(action.payload));
		default:
			return state;
	}
}

export default reducer;
