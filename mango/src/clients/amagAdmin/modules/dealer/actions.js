/* @flow */
import { Map, List } from 'immutable';
import {
	FIND_STARTED,
	FIND_SUCCEEDED,
	GET_DEALER_IMPORT_STARTED,
	GET_DEALER_IMPORT_SUCCEEDED,
	GET_STARTED,
	GET_SUCCEEDED,
	<PERSON><PERSON><PERSON><PERSON>_REQUESTED,
	SET_DEALER_CONFIG_STARTED,
	SET_DEALER_CONFIG_SUCCEEDED,
	SET_AMAG_RETAILER_FLAG_STARTED,
	SET_AMAG_RETAILER_FLAG_SUCCEEDED,
	SET_DEALER_FLAGS_STARTED,
	SET_DEALER_FLAGS_SUCCEEDED,
	INITIALIZE_DEALER_LABEL_STARTED,
	SET_DEALER_WEBSITE_ACTIVE_STARTED,
	SET_DEALER_WEBSITE_ACTIVE_SUCCEEDED,
	SET_DEALER_WEBSITE_CONFIG_STARTED,
	SET_DEALER_WEBSITE_CONFIG_SUCCEEDED,
	INITIALIZE_BRAND_WEBSITE_STARTED,
	SET_BRAND_WEBSITE_ACTIVE_STARTED,
	SET_BRAND_WEBSITE_ACTIVE_SUCCEEDED,
	SET_BRAND_WEBSITE_CONFIG_STARTED,
	SET_BRAND_WEBSITE_CONFIG_SUCCEEDED,
	DRAFT_CREATED,
	DEALER_DRAFT_UPDATED,
	DEALER_DRAFT_LOCATION_UPDATED,
	DEALER_DRAFT_LOCATION_DELETED,
	DEALER_DRAFT_LOCATION_DEPARTMENT_DELETED,
	DEALER_DRAFT_LOCATION_ADDED,
	DEALER_DRAFT_LOCATION_DEPARTMENT_ADDED,
	DEALER_DRAFT_LOCATION_RESET,
	DEALER_WEBSITE_DRAFT_UPDATED,
	DEALER_BRAND_WEBSITE_DRAFT_UPDATED,
	SET_DEALER_GROUP_LABELS_STARTED,
	SET_DEALER_GROUP_LABELS_SUCCEEDED,
	GET_ALL_DEALER_GROUPS_STARTED,
	GET_ALL_DEALER_GROUPS_SUCCEEDED,
	CREATE_DEALER_GROUP_STARTED,
	CREATE_DEALER_GROUP_SUCCEEDED,
	ACTIVATE_DEALER_GROUP_STARTED,
	ACTIVATE_DEALER_GROUP_SUCCEEDED,
	DEACTIVATE_DEALER_GROUP_STARTED,
	DEACTIVATE_DEALER_GROUP_SUCCEEDED,
	FIND_DEALER_GROUP_BY_LABEL_STARTED,
	FIND_DEALER_GROUP_BY_LABEL_SUCCEEDED,
	SET_DEALER_GROUP_CONFIG,
	SAVE_DEALER_GROUP_CONFIG_STARTED,
	SAVE_DEALER_GROUP_CONFIG_SUCCEEDED,
	FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_STARTED,
	FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_SUCCEEDED,
	SET_DEALER_GROUP_AMAG_FLAG_STARTED,
	SET_DEALER_GROUP_AMAG_FLAG_SUCCEEDED,

	SET_DEALER_CONFIG_INTERNAL_STARTED,
	SET_DEALER_CONFIG_INTERNAL_SUCCEEDED,
	GET_DEALER_CONFIG_INTERNAL_STARTED,
	GET_DEALER_CONFIG_INTERNAL_SUCCEEDED,
	RUN_DEALER_IMPORT_STARTED,
	RUN_DEALER_IMPORT_SUCCEEDED,
	DOWNLOAD_DEALER_IMPORT_XML_STARTED,
	DOWNLOAD_DEALER_IMPORT_XML_SUCCEEDED,
} from './constants';
import type { ImmutableIdentity, Identity } from '../../../../modules/request/types';
import type {
	FindAction,
	FindSucceededAction,

	GetAction,
	GetSucceededAction,
	ReloadRequestedAction,

	DealerWebsiteActiveStartedAction,
	DealerWebsiteActiveSucceededAction,
	DealerBrandWebsiteActiveStartedAction,
	DealerBrandWebsiteActiveSucceededAction,

	SetDealerConfigStartedAction,
	SetDealerConfigSucceededAction,
	SetAmagRetailerFlagStartedAction,
	SetAmagRetailerFlagSucceededAction,
	SetDealerFlagsStartedAction,
	SetDealerFlagsSucceededAction,
	SetDealerWebsiteConfigStartedAction,
	SetDealerWebsiteConfigSucceededAction,
	SetDealerBrandWebsiteConfigStartedAction,
	SetDealerBrandWebsiteConfigSucceededAction,

	DealerDraftUpdatedAction,
	DealerDraftLocationUpdatedAction,
	DealerDraftLocationDeletedAction,
	DealerDraftLocationDepartmentDeletedAction,
	DealerDraftLocationAddedAction,
	DealerDraftLocationDepartmentAddedAction,
	DealerDraftLocationResetAction,
	DealerWebsiteDraftUpdatedAction,
	DealerBrandWebsiteDraftUpdatedAction,
} from './types';

function find(): FindAction { // TODO: implement filters
	return {
		type: FIND_STARTED,
	};
}

function findSucceeded(payload: Map<string, *>): FindSucceededAction {
	return {
		type: FIND_SUCCEEDED,
		payload,
	};
}

function get(identity: ImmutableIdentity): GetAction {
	return {
		type: GET_STARTED,
		payload: identity,
	};
}


function getSucceeded(payload: Map<*, *>): GetSucceededAction {
	return {
		type: GET_SUCCEEDED,
		payload,
	};
}

function getDealerConfigInternal(identity: ImmutableIdentity) {
	return {
		type: GET_DEALER_CONFIG_INTERNAL_STARTED,
		payload: identity,
	};
}

function getDealerConfigInternalSucceeded(payload: Map<*, *>) {
	return {
		type: GET_DEALER_CONFIG_INTERNAL_SUCCEEDED,
		payload,
	};
}

function reloadRequested(identity: ImmutableIdentity): ReloadRequestedAction {
	return {
		type: RELOAD_REQUESTED,
		payload: identity,
	};
}

function setDealerConfigStarted(
	identity: ImmutableIdentity,
	config: Object,
): SetDealerConfigStartedAction {
	return {
		type: SET_DEALER_CONFIG_STARTED,
		payload: {
			identity,
			config,
		},
	};
}

function setDealerConfigSucceeded(): SetDealerConfigSucceededAction {
	return {
		type: SET_DEALER_CONFIG_SUCCEEDED,
	};
}

function setDealerConfigInternalStarted(
	identity: ImmutableIdentity,
	config: Object,
): SetDealerConfigStartedAction {
	return {
		type: SET_DEALER_CONFIG_INTERNAL_STARTED,
		payload: {
			identity,
			config,
		},
	};
}

function setDealerConfigInternalSucceeded(): SetDealerConfigSucceededAction {
	return {
		type: SET_DEALER_CONFIG_INTERNAL_SUCCEEDED,
	};
}

function setAmagRetailerFlagStarted(
	identity: ImmutableIdentity,
	isAmagRetailer: boolean,
): SetAmagRetailerFlagStartedAction {
	return {
		type: SET_AMAG_RETAILER_FLAG_STARTED,
		payload: {
			identity,
			isAmagRetailer,
		},
	};
}

function setAmagRetailerFlagSucceeded(
	identity: ImmutableIdentity,
	isAmagRetailer: boolean,
): SetAmagRetailerFlagSucceededAction {
	return {
		type: SET_AMAG_RETAILER_FLAG_SUCCEEDED,
		payload: {
			identity,
			isAmagRetailer,
		},
	};
}


function setDealerFlagsStarted(
	identity: ImmutableIdentity,
	flags: Map<any, any>,
): SetDealerFlagsStartedAction {
	return {
		type: SET_DEALER_FLAGS_STARTED,
		payload: {
			identity,
			flags,
		},
	};
}

function setDealerFlagsSucceeded(
	identity: ImmutableIdentity,
	flags: Map<any, any>,
): SetDealerFlagsSucceededAction {
	return {
		type: SET_DEALER_FLAGS_SUCCEEDED,
		payload: {
			identity,
			flags,
		},
	};
}

function setDealerWebsiteConfigStarted(
	identity: ImmutableIdentity,
	config: Object,
): SetDealerWebsiteConfigStartedAction {
	return {
		type: SET_DEALER_WEBSITE_CONFIG_STARTED,
		payload: {
			identity,
			config,
		},
	};
}

function setDealerWebsiteConfigSucceeded(): SetDealerWebsiteConfigSucceededAction {
	window.location.reload(true);

	return {
		type: SET_DEALER_WEBSITE_CONFIG_SUCCEEDED,
	};
}

function setBrandWebsiteConfigStarted(
	identity: ImmutableIdentity,
	config: Object,
	brand: string,
): SetDealerBrandWebsiteConfigStartedAction {
	return {
		type: SET_BRAND_WEBSITE_CONFIG_STARTED,
		payload: {
			identity,
			config,
			brand,
		},
	};
}

function setBrandWebsiteConfigSucceeded(brand: string): SetDealerBrandWebsiteConfigSucceededAction {
	return {
		type: SET_BRAND_WEBSITE_CONFIG_SUCCEEDED,
		payload: {
			brand,
		},
	};
}

function setDealerWebsiteActiveStarted(
	identity: ImmutableIdentity,
	active: boolean,
): DealerWebsiteActiveStartedAction {
	return {
		type: SET_DEALER_WEBSITE_ACTIVE_STARTED,
		payload: {
			identity,
			active,
		},
	};
}

function setDealerWebsiteActiveSucceeded(
	identity: ImmutableIdentity,
	active: boolean,
): DealerWebsiteActiveSucceededAction {
	return {
		type: SET_DEALER_WEBSITE_ACTIVE_SUCCEEDED,
		payload: {
			identity,
			active,
		},
	};
}

function setBrandWebsiteActiveStarted(
	dealerId: string,
	identity: ImmutableIdentity,
	active: boolean,
	brand: string,
): DealerBrandWebsiteActiveStartedAction {
	return {
		type: SET_BRAND_WEBSITE_ACTIVE_STARTED,
		payload: {
			dealerId,
			identity,
			active,
			brand,
		},
	};
}

function setBrandWebsiteActiveSucceeded(
	identity: ImmutableIdentity,
	active: boolean,
	brand: string,
): DealerBrandWebsiteActiveSucceededAction {
	return {
		type: SET_BRAND_WEBSITE_ACTIVE_SUCCEEDED,
		payload: {
			identity,
			active,
			brand,
		},
	};
}

function createDraft() {
	return {
		type: DRAFT_CREATED,
	};
}

function updateDealerDraft(key: Array<any>, value: any): DealerDraftUpdatedAction {
	return {
		type: DEALER_DRAFT_UPDATED,
		payload: {
			key,
			value,
		},
	};
}

function updateDealerDraftLocation(
	id: string,
	key: Array<any>,
	value: any,
): DealerDraftLocationUpdatedAction {
	return {
		type: DEALER_DRAFT_LOCATION_UPDATED,
		payload: {
			id,
			key,
			value,
		},
	};
}

function deleteDealerDraftLocation(
	id: string,
): DealerDraftLocationDeletedAction {
	return {
		type: DEALER_DRAFT_LOCATION_DELETED,
		payload: {
			id,
		},
	};
}

function deleteDealerDraftLocationDepartment(
	id: string,
	departmentKey: string,
): DealerDraftLocationDepartmentDeletedAction {
	return {
		type: DEALER_DRAFT_LOCATION_DEPARTMENT_DELETED,
		payload: {
			id,
			departmentKey,
		},
	};
}

function addDealerDraftLocation(
	id: string,
): DealerDraftLocationAddedAction {
	return {
		type: DEALER_DRAFT_LOCATION_ADDED,
		payload: {
			id,
		},
	};
}

function addDealerDraftLocationDepartment(
	id: string,
	departmentKey: string,
): DealerDraftLocationDepartmentAddedAction {
	return {
		type: DEALER_DRAFT_LOCATION_DEPARTMENT_ADDED,
		payload: {
			id,
			departmentKey,
		},
	};
}

function resetDealerDraftLocation(
	id: string,
	key: Array<any>,
	value: any,
): DealerDraftLocationResetAction {
	return {
		type: DEALER_DRAFT_LOCATION_RESET,
		payload: {
			id,
			key,
			value,
		},
	};
}

function updateDealerWebsiteDraft(key: Array<any>, value: any): DealerWebsiteDraftUpdatedAction {
	return {
		type: DEALER_WEBSITE_DRAFT_UPDATED,
		payload: {
			key,
			value,
		},
	};
}

function updateDealerBrandWebsiteDraft(
	key: Array<any>, value: any, brand: string,
): DealerBrandWebsiteDraftUpdatedAction {
	return {
		type: DEALER_BRAND_WEBSITE_DRAFT_UPDATED,
		payload: {
			key,
			value,
			brand,
		},
	};
}


function initializeBrandWebsiteStarted(
	dealerId: string,
	dealerLabel: string,
	brand: string,
) {
	return {
		type: INITIALIZE_BRAND_WEBSITE_STARTED,
		payload: {
			dealerId,
			dealerLabel,
			brand,
		},
	};
}

function initializeDealerLabelStarted(
	identity: ImmutableIdentity,
	dealerLabel: string,
) {
	return {
		type: INITIALIZE_DEALER_LABEL_STARTED,
		payload: {
			dealerLabel,
			identity,
		},
	};
}

function setDealerGroupLabelsStarted(
	identity: ImmutableIdentity,
	labels: Array<string>,
): Object {
	return {
		type: SET_DEALER_GROUP_LABELS_STARTED,
		payload: {
			labels,
			identity,
		},
	};
}

function setDealerGroupLabelsSucceeded(
	identity: ImmutableIdentity,
	labels: Array<string>,
): Object {
	return {
		type: SET_DEALER_GROUP_LABELS_SUCCEEDED,
		payload: {
			identity,
			labels,
		},
	};
}

function getAllDealerGroupsStarted(): Object {
	return {
		type: GET_ALL_DEALER_GROUPS_STARTED,
	};
}

function getAllDealerGroupsSucceeded(
	groups: List<Map<string, any>>,
): Object {
	return {
		type: GET_ALL_DEALER_GROUPS_SUCCEEDED,
		payload: {
			groups,
		},
	};
}

function createDealerGroupAction(
	label: string,
	name: string,
): Object {
	return {
		type: CREATE_DEALER_GROUP_STARTED,
		payload: {
			label,
			name,
		},
	};
}

function createDealerGroupSucceeded(
	label: string,
	name: string,
): Object {
	return {
		type: CREATE_DEALER_GROUP_SUCCEEDED,
		payload: {
			label,
			name,
		},
	};
}

function activateDealerGroupAction(
	identity: Identity,
): Object {
	return {
		type: ACTIVATE_DEALER_GROUP_STARTED,
		payload: {
			identity,
		},
	};
}

function activateDealerGroupSucceeded(
	identity: Identity,
): Object {
	return {
		type: ACTIVATE_DEALER_GROUP_SUCCEEDED,
		payload: {
			identity,
		},
	};
}

function deactivateDealerGroupAction(
	identity: Identity,
): Object {
	return {
		type: DEACTIVATE_DEALER_GROUP_STARTED,
		payload: {
			identity,
		},
	};
}

function deactivateDealerGroupSucceeded(
	identity: Identity,
): Object {
	return {
		type: DEACTIVATE_DEALER_GROUP_SUCCEEDED,
		payload: {
			identity,
		},
	};
}

function findDealerGroupByLabelAction(
	label: string,
): Object {
	return {
		type: FIND_DEALER_GROUP_BY_LABEL_STARTED,
		payload: {
			label,
		},
	};
}

function findDealerGroupByLabelSucceeded(
	config: Object,
): Object {
	return {
		type: FIND_DEALER_GROUP_BY_LABEL_SUCCEEDED,
		payload: {
			config,
		},
	};
}

function findDealerGroupByLabelWithDealersAction(
	label: string,
): Object {
	return {
		type: FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_STARTED,
		payload: {
			label,
		},
	};
}

function findDealerGroupByLabelWithDealersSucceeded(
	config: Object,
): Object {
	return {
		type: FIND_DEALER_GROUP_BY_LABEL_WITH_DEALERS_SUCCEEDED,
		payload: {
			config,
		},
	};
}

function setDealerGroupAmagFlagAction(
	identity: Identity,
	flag: boolean,
): Object {
	return {
		type: SET_DEALER_GROUP_AMAG_FLAG_STARTED,
		payload: {
			identity,
			flag,
		},
	};
}

function setDealerGroupAmagFlagSucceeded(
	identity: Identity,
	flag: boolean,
): Object {
	return {
		type: SET_DEALER_GROUP_AMAG_FLAG_SUCCEEDED,
		payload: {
			identity,
			flag,
		},
	};
}

function setDealerGroupConfigAction(
	identity: Identity,
	config: Object,
): Object {
	return {
		type: SET_DEALER_GROUP_CONFIG,
		payload: {
			identity,
			config,
		},
	};
}

function saveDealerGroupConfigAction(
	identity: Identity,
	config: Object,
): Object {
	return {
		type: SAVE_DEALER_GROUP_CONFIG_STARTED,
		payload: {
			identity,
			config,
		},
	};
}

function saveDealerGroupConfigSucceeded(
	identity: Identity,
	config: Object,
): Object {
	return {
		type: SAVE_DEALER_GROUP_CONFIG_SUCCEEDED,
		payload: {
			identity,
			config,
		},
	};
}

function getDealerImport() {
	return {
		type: GET_DEALER_IMPORT_STARTED,
	};
}

function getDealerImportSucceeded(payload) {
	return {
		type: GET_DEALER_IMPORT_SUCCEEDED,
		payload,
	};
}

function runDealerImport() {
	return {
		type: RUN_DEALER_IMPORT_STARTED,
	};
}

function runDealerImportSucceeded(payload) {
	return {
		type: RUN_DEALER_IMPORT_SUCCEEDED,
		payload,
	};
}

function downloadDealerImportXml() {
	return {
		type: DOWNLOAD_DEALER_IMPORT_XML_STARTED,
	};
}

function downloadDealerImportXmlSucceeded(payload) {
	return {
		type: DOWNLOAD_DEALER_IMPORT_XML_SUCCEEDED,
		payload,
	};
}


export {
	find,
	findSucceeded,

	getDealerImport,
	getDealerImportSucceeded,

	runDealerImport,
	runDealerImportSucceeded,

	downloadDealerImportXml,
	downloadDealerImportXmlSucceeded,

	get,
	getSucceeded,

	getDealerConfigInternal,
	getDealerConfigInternalSucceeded,

	reloadRequested,

	setDealerConfigStarted,
	setDealerConfigSucceeded,

	setAmagRetailerFlagStarted,
	setAmagRetailerFlagSucceeded,
	setDealerFlagsStarted,
	setDealerFlagsSucceeded,

	initializeDealerLabelStarted,

	setDealerWebsiteActiveStarted,
	setDealerWebsiteActiveSucceeded,

	setDealerWebsiteConfigStarted,
	setDealerWebsiteConfigSucceeded,

	initializeBrandWebsiteStarted,

	setBrandWebsiteActiveStarted,
	setBrandWebsiteActiveSucceeded,

	setBrandWebsiteConfigStarted,
	setBrandWebsiteConfigSucceeded,

	setDealerConfigInternalStarted,
	setDealerConfigInternalSucceeded,

	createDraft,
	updateDealerDraft,
	updateDealerDraftLocation,
	deleteDealerDraftLocation,
	deleteDealerDraftLocationDepartment,
	addDealerDraftLocation,
	addDealerDraftLocationDepartment,
	resetDealerDraftLocation,
	updateDealerWebsiteDraft,
	updateDealerBrandWebsiteDraft,
	setDealerGroupLabelsStarted,
	setDealerGroupLabelsSucceeded,
	getAllDealerGroupsStarted,
	getAllDealerGroupsSucceeded,

	createDealerGroupAction,
	createDealerGroupSucceeded,
	activateDealerGroupAction,
	activateDealerGroupSucceeded,
	deactivateDealerGroupAction,
	deactivateDealerGroupSucceeded,
	findDealerGroupByLabelAction,
	findDealerGroupByLabelSucceeded,
	findDealerGroupByLabelWithDealersAction,
	findDealerGroupByLabelWithDealersSucceeded,
	setDealerGroupAmagFlagAction,
	setDealerGroupAmagFlagSucceeded,
	setDealerGroupConfigAction,
	saveDealerGroupConfigSucceeded,
	saveDealerGroupConfigAction,
};
