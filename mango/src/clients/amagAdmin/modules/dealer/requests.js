/* @flow */
import 'isomorphic-fetch';
import * as request from '../../../../modules/request';
import type { Identity, ImmutableIdentity, RequestContext } from '../../../../modules/request/types';
import { DEALER_GROUP_TAG_PREFIX } from '../../../amagCore/modules/dealer-config/constants';

async function find(context: RequestContext, searchQuery: string, filter: any): any {
	// TODO @EB Filter is a List of type Filter
	const queryName = 'qry.dealer.find-all';
	const action = request.createAction(queryName, 0, {
		query: searchQuery || undefined,
		filter,
	});
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return {
		hits: request.getQueryData(queryName, res),
		total: request.getQueryData(queryName, res).length,
	};
}

async function setDealerConfig(context: RequestContext, identity: Identity, config: Object): any {
	const commandName = 'cmd.dealer.set-config';
	const action = request.createAction(commandName, 0, config);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);

	const rootNode = res.command.affected.find(item => item.key === 'root-node');
	return rootNode ? rootNode.value[1] : null;
}

async function setDealerWebsiteConfig(
	context: RequestContext,
	identity: Identity,
	config: Object,
): any {
	const commandName = 'cmd.dealer-website.set-config';
	const action = request.createAction(commandName, 0, config);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);

	const rootNode = res.command.affected.find(item => item.key === 'root-node');
	return rootNode ? rootNode.value[1] : null;
}

async function setBrandWebsiteConfig(
	context: RequestContext,
	identity: Identity,
	config: Object,
): any {
	const commandName = 'cmd.dealer-brand-website.set-config';
	const action = request.createAction(commandName, 0, config);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);

	const rootNode = res.command.affected.find(item => item.key === 'root-node');
	return rootNode ? rootNode.value[1] : null;
}

async function setDealerConfigInternal(
	context: RequestContext,
	identity: Identity,
	config: Object,
): any {
	const queryName = 'cmd.dealer.set-config-internal';
	const action = request.createAction(queryName, 0, config);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);

	const rootNode = res.command.affected.find(item => item.key === 'root-node');
	return rootNode ? rootNode.value[1] : null;
}

async function setDealerWebsiteActive(
	context: RequestContext,
	identity: Identity,
	active: boolean,
): any {
	const commandName = active ? 'cmd.dealer-website.activate' : 'cmd.dealer-website.deactivate';
	const action = request.createAction(commandName, 0);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		identity,
		active,
	};
}

async function initializeDealerLabel(
	context: RequestContext,
	identity: Identity,
	dealerLabel: string,
): any {
	const commandName = 'cmd.dealer.initialize-label';
	const action = request.createAction(commandName, 0, { 'dealer-label': dealerLabel });
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);
	return request.getRootAref(res);
}

async function initializeBrandWebsite(
	context: RequestContext,
	dealerId: string,
	dealerLabel: string,
	brand: string,
): any {
	const commandName = 'cmd.dealer-brand-website.initialize';
	const action = request.createAction(commandName, 0, { 'dealer-id': dealerId, 'dealer-label': dealerLabel, brand });
	const command = request.createCommand([action], null); // TODO @EB check ver
	const req = request.create({ command });
	const res = await request.dispatch(context, req);
	return request.getRootAref(res);
}

async function setBrandWebsiteActive(
	context: RequestContext,
	identity: Identity,
	active: boolean,
): any {
	const commandName = active ? 'cmd.dealer-brand-website.activate' : 'cmd.dealer-brand-website.deactivate';
	const action = request.createAction(commandName, 0);
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		identity,
		active,
	};
}

async function get(context: RequestContext, identity: ImmutableIdentity): any {
	const queryName = 'qry.dealer.get-with-websites';
	const action = request.createAction(queryName, 0, { identity });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	const data = request.getQueryData(queryName, res);
	return data;
}

async function getDealerConfigInternal(context: RequestContext, identity: ImmutableIdentity): any {
	const queryName = 'qry.dealer.get-config-internal';
	const action = request.createAction(queryName, 0, { identity });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	const data = request.getQueryData(queryName, res);
	return data;
}

async function setAmagRetailerFlag(
	context: RequestContext,
	identity: ImmutableIdentity,
	amagretailer: boolean,
): any {
	const commandName = 'cmd.dealer.set-amagretailer-flag';
	const action = request.createAction(commandName, 0, { amagretailer });
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		identity,
	};
}

async function setDealerFlags(
	context: RequestContext,
	identity: ImmutableIdentity,
	flags: Object,
): any {
	const commandName = 'cmd.dealer.set-flags';
	const action = request.createAction(commandName, 0, { flags });
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		identity,
	};
}

async function setDealerGroupLabels(
	context: RequestContext,
	identity: ImmutableIdentity,
	dealerGroupLabels: Array<string>,
): any {
	const commandName = 'cmd.dealer.set-dealer-group-labels';
	const action = request.createAction(commandName, 0, { 'dealer-group-labels': dealerGroupLabels });
	const command = request.createCommand([action], { identity, ver: 99999 }); // TODO @EB check ver
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		identity,
	};
}

async function findAllDealerGroups(context: RequestContext) {
	const queryName = 'qry.dealerGroup.findAll';
	const action = request.createAction(queryName, 0, {});
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return {
		data: request.getQueryData(queryName, res).hits,
	};
}

async function createDealerGroup(
	context: RequestContext,
	label: string,
	name: string,
): any {
	const commandName = 'cmd.dealerGroup.create';
	const prefixedLabel = label.startsWith(DEALER_GROUP_TAG_PREFIX) ?
		label : DEALER_GROUP_TAG_PREFIX + label;
	const action = request.createAction(
		commandName,
		0,
		{ label: prefixedLabel, name },
	);
	const command = request.createCommand([action], null);
	const req = request.create({ command });
	await request.dispatch(context, req);
	return {
		label: prefixedLabel,
		name,
	};
}

async function activateDealerGroupWebsite(
	context: RequestContext,
	identity: Identity,
): any {
	const commandName = 'cmd.dealerGroup.activateWebsite';
	const action = request.createAction(commandName, 0, {});
	const command = request.createCommand([action], { identity, ver: 99999 });
	const req = request.create({ command });
	await request.dispatch(context, req);
	return true;
}

async function deactivateDealerGroupWebsite(
	context: RequestContext,
	identity: Identity,
): any {
	const commandName = 'cmd.dealerGroup.deactivateWebsite';
	const action = request.createAction(commandName, 0, {});
	const command = request.createCommand([action], { identity, ver: 99999 });
	const req = request.create({ command });
	await request.dispatch(context, req);
	return false;
}

async function setDealerGroupWebsiteConfig(
	context: RequestContext,
	identity: Identity,
	websiteConfig: Object,
): any {
	const commandName = 'cmd.dealerGroup.setWebsiteConfig';
	const action = request.createAction(commandName, 0, { websiteConfig });
	const command = request.createCommand([action], { identity, ver: 99999 });
	const req = request.create({ command });
	await request.dispatch(context, req);
	return websiteConfig;
}

async function setDealerGroupAmagFlag(
	context: RequestContext,
	identity: Identity,
	amag: boolean,
): any {
	const commandName = 'cmd.dealerGroup.setAmagFlag';
	const action = request.createAction(commandName, 0, { amag });
	const command = request.createCommand([action], { identity, ver: 99999 });
	const req = request.create({ command });
	await request.dispatch(context, req);
	return amag;
}

async function findDealerGroupByLabel(
	context: RequestContext,
	label: string,
) {
	const queryName = 'qry.dealerGroup.findByLabel';
	const action = request.createAction(queryName, 0, { label });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return request.getQueryData(queryName, res);
}

async function findDealerGroupByLabelWithDealers(
	context: RequestContext,
	label: string,
) {
	const queryName = 'qry.dealerGroup.findByLabelWithDealers';
	const action = request.createAction(queryName, 0, { label });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	return request.getQueryData(queryName, res);
}

async function resolveDealerId(context: Object, customerNr: string): any {
	const queryName = 'qry.dealer.resolve-id';
	const action = request.createAction(queryName, 0, { 'customer-nr': customerNr });
	const query = request.createQuery([action]);
	const req = request.create({ query });
	const res = await request.dispatch(context, req);
	const data = request.getQueryData(queryName, res);
	return data ? { 'customer-nr': customerNr, 'dealer-id': data['dealer-id'] } : { 'customer-nr': customerNr, 'dealer-id': false };
}

export {
	find,
	get,
	getDealerConfigInternal,

	setDealerConfig,

	initializeDealerLabel,
	setDealerWebsiteActive,
	setDealerWebsiteConfig,
	setAmagRetailerFlag,
	setDealerFlags,

	initializeBrandWebsite,
	setBrandWebsiteActive,
	setBrandWebsiteConfig,
	setDealerConfigInternal,

	setDealerGroupLabels,
	findAllDealerGroups,
	createDealerGroup,
	activateDealerGroupWebsite,
	deactivateDealerGroupWebsite,
	setDealerGroupWebsiteConfig,
	setDealerGroupAmagFlag,
	findDealerGroupByLabel,
	findDealerGroupByLabelWithDealers,

	resolveDealerId,
};
