/* @flow */
import React from 'react';
import { Formik } from 'formik';
import { List, Map } from 'immutable';
import { ImageUpload } from '../../../../../modules/files/upload';
import { PostElementsEditor } from '../../../../../modules/elements';
import { EditorStatus } from '../../../../../modules/editor';
import { Link } from '../../../../../tango/routing';

import BackgroundPreview from '../../../../amagCore/modules/ArticleEditor/BackgroundPreview';
import EditorMenu, {
	DialogItem,
	Dialogs,
} from '../../../../../modules/editorMenu';
import * as AmagDialogs from '../../../../amagCore/modules/editorMenuDialogs';
import {
	PublishLoadingModal,
	SaveErrorModal,
} from '../../../../amagCore/modules/article-modals';
import { TextField } from '../../../../amagCore/modules/ArticleEditor/form/TextField';
import { TextAreaField } from '../../../../amagCore/modules/ArticleEditor/form/TextAreaField';
import type { ArticleEditorConfig } from '../../../../amagCore/modules/ArticleEditor/types';
import { DEFAULT_ARTICLE_EDITOR_CONFIG } from '../../../../amagCore/modules/ArticleEditor/defaultArticleEditorConfig';
import { validate } from '../../../../amagCore/modules/ArticleEditor/validate';
import {
	TITLE_MAX_LENGTH,
	LEADTEXT_MAX_LENGTH,
} from '../../../../amagCore/modules/article/constants';
import {
	OCCASIONS_AMAG_RETAIL_ALIAS_REGEX,
	CONTACT_AMAG_RETAIL_ALIAS_REGEX,
} from '../../../../amagCore/modules/alias/constants';
import { NoIndex } from '../../../../amagCore/modules/NoIndex';
import { getRelatedDashboardsSubSortAttrs } from '../../../../amagCore/modules/dashboard/dashboards';

import PublishDialog from './PublishDialogContainer';

type Props = {
	articleId: string,
	theme: Object,
	aspect: ?number,
	isStaticAliasFunc?: (aliases: Array<string>, dealerLabel: string) => boolean,
	config?: ArticleEditorConfig,
	dealerId: string,
	dealerTag: string,
	t: Function,
	save: Object,
	draft: Object,
	meta: Object,
	published: Object,
	stateIsChanging: boolean,
	update: Function,
	deleteImage: Function,
	publishDate: any,
	unpublishDate: any,
	updatePublishDate: Function,
	updateUnpublishDate: Function,
	publishWithAlias: Function,
	publish: Function,
	unpublish: Function,
	archive: Function,
	navigateBack: Function,
	isAmagDealer: boolean,
	userGroups: List<string>,
	dialogClassName?: string,
};

function isStaticAliasCoreFunc(aliases: Array<string> = []): boolean {
	return aliases.some(
		alias =>
			new RegExp('^amag/(de|en|it|fr)/faq$').test(alias) ||
			new RegExp('^amag/(de|en|it|fr)/user-manual$').test(alias) ||
			new RegExp(OCCASIONS_AMAG_RETAIL_ALIAS_REGEX).test(alias) ||
			new RegExp(CONTACT_AMAG_RETAIL_ALIAS_REGEX).test(alias),
	);
}

const getInitialPosition = (
	userGroups: List<string> = new List(),
	meta: Map<string, any> = new Map(),
): number | void => {
	// always send empty value after initial publication
	if (meta.get('number-of-publications', 0) > 0) {
		return undefined;
	}
	return userGroups.some(group => group === 'amag-retailer') ? 2 : 1;
};

type State = {
	position?: number,
	horizontalPosition?: number,
};

class ArticleEditor extends React.Component {
	props: Props;
	state: State = {
		position: getInitialPosition(this.props.userGroups, this.props.meta),
		horizontalPosition: getInitialHorizontalPosition(this.props.userGroups, this.props.meta),
	};

	setPosition = (position?: number) => {
		this.setState({ position: position ? parseInt(position, 10) : undefined });
	};

	render() {
		const {
			draft,
			articleId,
			dealerTag,
			theme,
			aspect = 16 / 10,
			save,
			published,
			stateIsChanging,
			t,
			dealerId,
			meta,
			publishDate,
			unpublishDate,
			updatePublishDate,
			updateUnpublishDate,
			publishWithAlias,
			publish,
			unpublish,
			navigateBack,
			archive,
			update,
			deleteImage,
			isStaticAliasFunc,
			isAmagDealer,
			config = DEFAULT_ARTICLE_EDITOR_CONFIG,
			dialogClassName,
		} = this.props;

		const tags = draft.get('tags');
		const aliases = meta.get('aliases');
		const isStaticAlias =
			isStaticAliasCoreFunc(aliases) ||
			(isStaticAliasFunc ? isStaticAliasFunc(aliases, dealerTag) : false);

		const initialValues = {
			title: draft.getIn(['article', 'title']),
			leadText: draft.getIn(['article', 'leadText']),
		};

		const initialErrors = validate(initialValues);
		const isInitialValid = Object.keys(initialErrors).length === 0;
		const { editorConfig } = config;
		const elementsConfig = {
			...editorConfig,
			availablePlugins: [
				...((editorConfig && editorConfig.availablePlugins) || []),
				'iframe',
			],
		};

		return (
			<Formik
				initialValues={initialValues}
				validate={validate}
				isInitialValid={isInitialValid}
				onSubmit={() => {
					const subPositionAttrs = getRelatedDashboardsSubSortAttrs(dealerId, isAmagDealer, tags);
					if (isStaticAlias) {
						publish(this.state.position, subPositionAttrs);
					} else {
						publishWithAlias(this.state.position, subPositionAttrs);
					}
				}}
			>
				{(formikProps) => {
					const {
						isValid,
						errors,
						values,
						handleChange,
						handleBlur,
						handleSubmit,
					} = formikProps;

					return (
						<React.Fragment>
							<NoIndex />
							<EditorStatus
								save={save}
								isPublishAhead={!draft.equals(published)}
								stateIsChanging={stateIsChanging}
							/>
							<Link className={theme.backBtn} to="home">
								back
							</Link>

							{isStaticAlias && (
								<EditorMenu text="...">
									<DialogItem
										text={t('amag.modules.articleEditor.publishMenuItem')}
									>
										<AmagDialogs.AliasPostPublishDialog
											seo={draft.getIn(['seo'])}
											isFetching={stateIsChanging}
											handleSubmit={handleSubmit}
											isValid={isValid}
											tags={tags}
										/>
									</DialogItem>
								</EditorMenu>
							)}

							{!isStaticAlias && (
								<EditorMenu text="...">
									<DialogItem
										text={t('amag.modules.articleEditor.publishMenuItem')}
									>
										<PublishDialog
											className={dialogClassName}
											publishDate={publishDate}
											unpublishDate={unpublishDate}
											updatePublishDate={updatePublishDate}
											updateUnpublishDate={updateUnpublishDate}
											handleSubmit={handleSubmit}
											isValid={isValid}
											articleId={articleId}
											tags={tags}
											dealerId={dealerId}
											dealerLabel={dealerTag}
											draft={draft}
											published={published}
											meta={meta}
											isFetching={stateIsChanging}
											config={config.publishDialogConfig}
											onPositionUpdate={this.setPosition}
											position={this.state.position}
											horizontalPosition={this.state.horizontalPosition}
										/>
									</DialogItem>
									<DialogItem
										text={t('amag.modules.articleEditor.unpublishMenuItem')}
									>
										<Dialogs.ConfirmationDialog
											question={t(
												'amag.modules.articleEditor.unpublishQuestion',
											)}
											onAccept={() => unpublish()}
											onReject={() => navigateBack()}
										/>
									</DialogItem>
									<DialogItem
										text={t('amag.modules.articleEditor.archiveMenuItem')}
									>
										<Dialogs.ConfirmationDialog
											question={t('amag.modules.articleEditor.archiveQuestion')}
											onAccept={() => archive()}
											onReject={() => navigateBack()}
										/>
									</DialogItem>
								</EditorMenu>
							)}
							<div className={theme.editor}>
								<ImageUpload
									className={theme.headerImage}
									image={draft.getIn(['article', 'image'])}
									onImageUpload={imageData =>
										update(['article', 'image'], imageData)
									}
									onImageDelete={() => deleteImage(['article', 'image'])}
									onCrop={transformations =>
										update(
											['article', 'image', 'transformations'],
											transformations,
										)
									}
									aspect={aspect}
									dimensions={
										config.headerImageDimensions ||
										DEFAULT_ARTICLE_EDITOR_CONFIG.headerImageDimensions
									}
									previewElement={BackgroundPreview}
									previewElementClassName={theme.backgroundImage}
									isWide
								/>
								<TextField
									name="title"
									placeholder={t('amag.modules.articleEditor.titlePlaceholder')}
									className={theme.title}
									maxLength={TITLE_MAX_LENGTH}
									theme={theme}
									value={values.title}
									defaultValue={draft.getIn(['article', 'title']) || ''}
									error={errors.title}
									onBlur={handleBlur}
									onChange={(event) => {
										update(['article', 'title'], event.target.value);
										handleChange(event);
									}}
								/>
								<TextAreaField
									name="leadText"
									placeholder={t(
										'amag.modules.articleEditor.leadTextPlaceholder',
									)}
									className={theme.leadText}
									theme={theme}
									value={values.leadText}
									defaultValue={draft.getIn(['article', 'leadText']) || ''}
									error={errors.leadText}
									maxLength={LEADTEXT_MAX_LENGTH}
									onBlur={handleBlur}
									onChange={(event) => {
										update(['article', 'leadText'], event.target.value);
										handleChange(event);
									}}
								/>
								<div className={theme.contentContainer}>
									<PostElementsEditor
										identity={[articleId, 'article']}
										elements={draft.get('elements')}
										config={elementsConfig}
									/>
								</div>
							</div>
							<PublishLoadingModal />
							<SaveErrorModal />
						</React.Fragment>
					);
				}}
			</Formik>
		);
	}
}

export default ArticleEditor;
