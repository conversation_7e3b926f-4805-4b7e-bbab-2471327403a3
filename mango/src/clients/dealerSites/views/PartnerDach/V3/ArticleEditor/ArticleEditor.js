/* @flow */
import React from 'react';
import withStyles from 'isomorphic-style-loader/lib/withStyles';
import { Editor, ArticleEditor as CoreArticleEditor } from '../../../../../amagCore/modules/ArticleEditor';
import getEditorEditConfig from '../../../../modules/getEditorEditConfig';
import Loader from '../../../../modules/Loader';
import s from './articleEditor.css';

const articleEditorConfig = {
	publishDialogConfig: {
		showTitleVisibility: true,
		showTitlePosition: true,
		showTitleHorizontalPosition: true,
		showTitleSize: true,
		showTitleColor: true,
		availablePositions: ['left', 'center'],
		availableHorizontalPositions: ['top', 'middle', 'bottom'],
	},
	editorConfig: getEditorEditConfig(),
};

type Props = {
	articleId: string,
};

function ArticleEditor({ articleId }: Props) {
	return (
		<div className={s.editorContainer}>
			<Editor
				id={articleId}
				loaderComponent={Loader}
			>
				{() => (
					<CoreArticleEditor
						articleId={articleId}
						theme={s}
						aspect={null}
						config={articleEditorConfig}
					/>
				)}
			</Editor>
		</div>
	);
}

export default (withStyles(s)(ArticleEditor): ReactComponent<Props>);
