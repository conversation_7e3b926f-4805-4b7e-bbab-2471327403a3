@import '../variables.css';
@import '../images.css';

:root {
	--editorMenu-publishDialog-fontFamily: var(--font-text);
	--editorMenu-publishDialog-fontSize: var(--default-font-size);
	--editorMenu-publishDialog-lineHeight: var(--default-line-height);

	--editorMenu-publishDialog-hintFontFamily: var(--font-text);
	--editorMenu-publishDialog-hintColor: color(var(--black) lightness(60%));
	--editorMenu-publishDialog-hintFontSize: var(--small-font-size);
	--editorMenu-publishDialog-hintLineHeight: var(--small-line-height);

	--editorMenu-publishDialog-labelColor: color(var(--black) lightness(60%));

	--editorMenu-publishDialog-metaDescriptionInputFontFamily: var(--font-text);
	--editorMenu-publishDialog-metaDescriptionInputColor: var(--text-dark-gray);
	--editorMenu-publishDialog-metaDescriptionInputFontSize: var(--default-font-size);
	--editorMenu-publishDialog-metaDescriptionInputLineHeight: var(--default-line-height);
	--editorMenu-publishDialog-metaDescriptionInputBackground: color(var(--black) alpha(-95%));

	--editorMenu-publishDialog-hightlightValueColor: var(--medium-gray);
	--editorMenu-publishDialog-clearButtonIcon: var(--iconDelete);
	--editorMenu-publishDialog-clearButtonIconBackground: color(var(--black) alpha(-100%));
	--editorMenu-publishDialog-hoverClearButtonIconBackground: color(var(--black) alpha(-90%));

	--settingsContainer-background: rgb(242,242,242);

	--editorMenu-publishDialog-deleteButtonIcon: var(--iconDelete);
	--editorMenu-publishDialog-deleteButtonIconBackground: color(var(--black) alpha(-100%));
	--editorMenu-publishDialog-hoverDeleteButtonIconBackground: color(var(--black) alpha(-90%));

	--editorMenu-publishDialog-saveButtonIcon: var(--iconDone);
	--editorMenu-publishDialog-saveButtonIconBackground: var(--success-color);
	--editorMenu-publishDialog-hoverSaveButtonIconBackground: color(var(--success-color) whiteness(20%));


	--editorMenu-publishDialog-conflictIcon: var(--iconNotOk);
	--editorMenu-publishDialog-nonConflictIcon: var(--iconOk);

	--editorMenu-publishDialog-select {
		padding: 10px 25px 13px 13px;
		background: url('../img/icons/arrow-select.svg') no-repeat #FFFFFF;
		background-position: calc(100% - 10px) 16px;
		background-size: 13px;
	}

	--editorMenu-publishDialog-radio {
		& span {
			vertical-align: text-bottom;
		}
	}

	--editorMenu-publishDialog-contractButton {
		border: 1px solid color(var(--black) alpha(-90%));
		border-radius: 4px;
		padding: 3px 20px;
		font-family: var(--font-text);
		font-size: var(--smaller-font-size);
		line-height: var(--smaller-line-height);
	}

	--editorMenu-publishDialog-backButton {
		color: var(--primary-color);
		padding-left: 23px;
		background: url('../img/icons/back_colored.svg') no-repeat #FFFFFF;
		background-position: 0px center;
	}

	--editorMenu-publishDialog-input {
		font-family: var(--font-text);
		color: var(--text-dark-gray);
		font-size: var(--default-font-size);
		line-height: var(--default-line-height);
		border: 1px solid var(--greyish-light);
		background-color: var(--white);
	}

	--editorMenu-publishDialog-settingsIcon {
		margin-top: 5px;
	}

	& .publishDialog {
		width: auto !important;
	}

	& .dateTimePicker {
		& :global {
			& .react-datepicker__time-container .react-datepicker__time .react-datepicker__time-box ul.react-datepicker__time-list {
				padding: 0px;
	
				display: flex;
				flex-direction: column;
	
				& .react-datepicker__time-list-item {
					padding-left: 0px;
					padding-right: 0px;
				}
			}
		}
	}
}
