/* @flow */
import React from 'react';
import { connect } from 'react-redux';
import { fromJS, List, Map } from 'immutable';
import classNames from 'classnames';
import withStyles from 'isomorphic-style-loader/lib/withStyles';

import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import ToggleContent from '../../../../../modules/toggle/fac/Content';
import type { Tags } from '../../../../../modules/post/tags';
import { localize } from '../../../../../modules/i18n';
import { LocalizeProps } from '../../../../../modules/i18n/types';
import { EditMetaDescription } from '../../../../../modules/seo';

import hasPermission from '../../authorization/hasPermission';
import { CAN_MANAGE_ALL_ARTICLES } from '../../authorization/permissions';
import { getBrand } from '../../config/selectors';

import type { PublishDialogConfig } from '../types';

import PublicationTriggersSettings, {
	PUBLICATION_TRIGGERS_CONTENT_NAME,
} from './PublicationTriggersSettings';
import PublicationSettingsSummary from './PublicationTriggersSettings/PublicationSettingsSummary';
import {
	getPublishDateText,
	hasPublicationDate,
} from './PublicationTriggersSettings/Utils';
import VisibilitySettings, {
	VISIBILITY_CONTENT_NAME,
} from './VisibilitySettings';
import VisibilitySummary from './VisibilitySettings/VisibilitySummary';
import FlagSettings, { FLAG_CONTENT_NAME } from './FlagSettings';
import FlagSummary from './FlagSettings/FlagSummary';
import HighlightCheckbox from './HighlightCheckbox';
import TitlePositionSelect from './TitlePositionSelect';
import Alias from './Alias';
import LanguageSelect from './LanguageSelect';
import styles from './PublishDialog.css';
import contentStyles from './PublishDialogContent.css';
import { namespace } from './i18n';
import { HIGHLIGHT_TAG } from './constants';
import { TitleVisibilitySelect } from './TitleVisibilitySelect';
import { TitleSizeSelect } from './TitleSizeSelect';
import { TitleColorSelect } from './TitleColorSelect';
import { usePublishDialog } from './usePublishDialog';
import { useHighlightCount } from '../../editorMenuDialogs/useHighlightCount';
import { FEATURE_FLAGS } from '../../../../FEATURE_FLAGS';
import TitleHorizontalPositionSelect from './TitleHorizontalPositionSelect';

const MAIN_CONTENT_NAME = 'amagCore/publishDialog/main';

function getHasTooManyHighlights(
	hasHighlightTag: Boolean,
	highlightIds: Array<string>,
	articleId: string,
	hideAllOptions?: boolean,
) {
	if (hideAllOptions) {
		return false;
	}
	if (hasHighlightTag) {
		return highlightIds.filter(id => id !== articleId).length >= 5;
	}
	return false;
}

type Props = {
	publishDate: any,
	unpublishDate: any,
	updatePublishDate: Function,
	updateUnpublishDate: Function,
	publish: Function,
	isValid: boolean,
	articleId: string,
	tags: Tags,
	dealerId: string,
	dealerLabel: string,
	draft: Map<any, any>,
	published: Map<any, any>,
	meta: Map<any, any>,
	isFetching: boolean,
	config?: ?PublishDialogConfig,
	isAmagDealer: boolean,
	hasDealerWebsite: boolean,
	activeBrandWebsites: List<string>,
	hasAliasConflict: boolean,
	userPermissions: List<any>,
	addTag: Function,
	removeTag: Function,
	replaceTag: Function,
	publish: Function,
	updateDraft: Function,
	updateSeoData: Function,
	updateSortAttribute: Function,
	openToggle: Function,
	closeToggle: Function,
	position: number,
	onPositionUpdate: number => void,
	className?: string,
	dealers?: List<any>,
	brand?: string,
};

const PublishDialog = ({
	publishDate,
	unpublishDate,
	updatePublishDate,
	updateUnpublishDate,
	handleSubmit,
	isValid,
	articleId,
	dealerLabel,
	meta,
	draft,
	tags,
	dealerId,
	isFetching,
	replaceTag,
	addTag,
	removeTag,
	updateDraft,
	updateSeoData,
	activeBrandWebsites,
	hasAliasConflict,
	hasDealerWebsite,
	isAmagDealer,
	userPermissions,
	config = {},
	t,
	locale,
	className,
	dealers = new List(),
	brand,
	...props
}: Props & LocalizeProps) => {
	const openSettings = (name: string) => {
		props.closeToggle(MAIN_CONTENT_NAME);
		props.openToggle(name);
	};
	const closeSettings = (name: string) => {
		props.closeToggle(name);
		props.openToggle(MAIN_CONTENT_NAME);
	};

	const aliases = meta.get('aliases');
	const publicationState = meta.get('state');
	const publicationStateLastModified = meta.get('state-last-modified');

	const seo = draft.getIn(['seo']);

	const flagText = draft.getIn(['article', 'simple', 'flagText']);
	const flagColor = draft.getIn(['article', 'simple', 'flagColor']);
	const flagBackground = draft.getIn([
		'article',
		'simple',
		'flagBackgroundColor',
	]);

	const titlePosition = draft.getIn(['article', 'simple', 'titlePosition']);
	const titleHorizontalPosition = draft.getIn([
		'article',
		'simple',
		'titleHorizontalPosition',
	]);
	const titleVisiblity = draft.getIn(['article', 'simple', 'titleVisibility']);

	console.log('titlePosition_', titlePosition);
	console.log('titleHorizontalPosition_', titleHorizontalPosition);

	const hasAlias = aliases && aliases.size > 0;
	const hasHighlightTag = tags.includes(HIGHLIGHT_TAG);

	const titleSize = draft.getIn(['article', 'simple', 'titleSize']);
	const titleColor = draft.getIn(['article', 'simple', 'titleColor']);

	const isAdmin = hasPermission(userPermissions, [CAN_MANAGE_ALL_ARTICLES]);

	const { highlightIds } = useHighlightCount(brand);
	const hasTooManyHighlights = getHasTooManyHighlights(
		hasHighlightTag,
		highlightIds,
		articleId,
		config.hideAllOptions,
	);
	const hasUnpublishDate =
		!FEATURE_FLAGS.AMAG.AMAG_391_REQUIRE_UNPUBLISH_DATE ||
		hasPublicationDate(unpublishDate, config);

	const publishDisabled =
		isFetching ||
		hasAliasConflict ||
		!isValid ||
		hasTooManyHighlights ||
		!hasUnpublishDate;

	const { triggerPublish, triggerStagingPublish } = usePublishDialog(
		tags,
		addTag,
		removeTag,
		handleSubmit,
		dealers,
	);

	return (
		<div className={classNames(styles.publishDialog, className)}>
			<ToggleContent name={MAIN_CONTENT_NAME} openPerDefault>
				{isOpen => {
					const actualClassName = classNames(contentStyles.content, {
						[contentStyles.opened]: isOpen,
						[contentStyles.closed]: !isOpen,
					});
					return (
						<div className={actualClassName}>
							{!config.hideAllOptions && (
								<React.Fragment>
									<div className={styles.settings}>
										<span
											className={classNames(
												styles.settingsIcon,
												styles.publicationSettingsIcon,
											)}
										/>
										<div className={styles.settingsContent}>
											<PublicationSettingsSummary
												publishDate={publishDate}
												unpublishDate={unpublishDate}
												className={styles.summary}
											/>
											{!hasUnpublishDate && (
												<div className={styles.error}>
													{t('unpublishDateMissing')}
												</div>
											)}
											<button
												type="button"
												className={styles.editButton}
												onClick={() =>
													openSettings(PUBLICATION_TRIGGERS_CONTENT_NAME)
												}
											>
												{t('edit')}
											</button>
										</div>
									</div>
									<div className={styles.settings}>
										<span
											className={classNames(
												styles.settingsIcon,
												styles.visibilityIcon,
											)}
										/>
										<div className={styles.settingsContent}>
											<VisibilitySummary
												tags={tags}
												dealerId={dealerId}
												isAmagDealer={isAmagDealer}
												className={styles.summary}
											/>
											<button
												type="button"
												className={styles.editButton}
												onClick={() => openSettings(VISIBILITY_CONTENT_NAME)}
											>
												{t('edit')}
											</button>
										</div>
									</div>
									<div className={styles.separator} />
									{config.showFlagText && (
										<div className={styles.settings}>
											<span
												className={classNames(
													styles.settingsIcon,
													styles.flagIcon,
												)}
											/>
											<div className={styles.settingsContent}>
												<FlagSummary
													text={flagText}
													color={flagColor}
													backgroundColor={flagBackground}
													defaultColor={config.defaultFlagColor}
													defaultBackgroundColor={
														config.defaultFlagBackgroundColor
													}
													className={styles.summary}
												/>
												<button
													type="button"
													className={styles.editButton}
													onClick={() => openSettings(FLAG_CONTENT_NAME)}
												>
													{t('edit')}
												</button>
											</div>
										</div>
									)}
									<div className={styles.settings}>
										<span
											className={classNames(
												styles.settingsIcon,
												styles.highlightIcon,
											)}
										/>
										<div className={styles.settingsContent}>
											<HighlightCheckbox
												hasHighlightTag={hasHighlightTag}
												onTagAdd={addTag}
												onTagRemove={removeTag}
												disabled={hasTooManyHighlights}
											/>
										</div>
									</div>
									{config.showTitleVisibility && hasHighlightTag && (
										<TitleVisibilitySelect
											option={titleVisiblity}
											onChange={option =>
												updateDraft(
													['article', 'simple', 'titleVisibility'],
													option,
												)
											}
										/>
									)}
									{config.showTitlePosition && hasHighlightTag && (
										<TitlePositionSelect
											position={titlePosition}
											onChangePosition={pos =>
												updateDraft(['article', 'simple', 'titlePosition'], pos)
											}
											availablePositions={config.availablePositions}
										/>
									)}
									{config.showTitleHorizontalPosition && hasHighlightTag && (
										<TitleHorizontalPositionSelect
											position={titleHorizontalPosition}
											onChangeHorizontalPosition={pos => {
												console.log('onChangeHorizontalPosition_', pos);
												// return updateDraft(
												// 	['article', 'simple', 'horizontalTitlePosition'],
												// 	pos,
												// );
											}}
											availablePositions={config.availableHorizontalPositions}
										/>
									)}
									{config.showTitleSize && hasHighlightTag && (
										<TitleSizeSelect
											option={titleSize}
											onChange={selected =>
												updateDraft(
													['article', 'simple', 'titleSize'],
													selected,
												)
											}
										/>
									)}

									{config.showTitleColor && hasHighlightTag && (
										<TitleColorSelect
											option={titleColor}
											onChange={selected =>
												updateDraft(
													['article', 'simple', 'titleColor'],
													selected,
												)
											}
										/>
									)}

									<div className={styles.separator} />

									<LanguageSelect
										tags={tags}
										onReplaceTag={replaceTag}
										disabled={hasAlias}
									/>
									<div className={styles.hint}>{t('languageHint')}</div>

									<Alias
										articleId={articleId}
										title={draft.getIn(['article', 'title'], '')}
										tags={tags}
										dealerLabel={dealerLabel}
										aliases={aliases}
										publishDate={publishDate}
										onResolveConflict={handleSubmit}
										hasAliasConflict={hasAliasConflict}
									/>
									{hasAliasConflict && (
										<small
											className={classNames(styles.hint, styles.errorHint)}
										>
											{t('aliasHint')}
										</small>
									)}
									<EditMetaDescription
										className={styles.metaDescription}
										seoData={seo || fromJS({})}
										onChange={(path, value) => updateSeoData(path, value)}
										max={156}
									/>
								</React.Fragment>
							)}
							{!hasUnpublishDate && (
								<div className={styles.error}>{t('unpublishDateMissing')}</div>
							)}
							<button
								className={classNames(
									styles.publishDialogButton,
									'colored-button',
								)}
								disabled={publishDisabled}
								type="button"
								onClick={triggerPublish}
							>
								{t('publishButton', {
									publishDate: getPublishDateText(publishDate, t, locale),
								})}
							</button>
							{isAdmin && (
								<button
									className={classNames(
										styles.publishDialogButton,
										'colored-button',
									)}
									disabled={publishDisabled}
									type="button"
									onClick={triggerStagingPublish}
								>
									{t('publishWithApproval')}
								</button>
							)}
						</div>
					);
				}}
			</ToggleContent>
			<PublicationTriggersSettings
				className={contentStyles.content}
				publishDate={publishDate}
				unpublishDate={unpublishDate}
				publicationState={publicationState}
				publicationStateLastModified={publicationStateLastModified}
				onPublishDateChanged={updatePublishDate}
				onUnpublishDateChanged={updateUnpublishDate}
				onClose={() => closeSettings(PUBLICATION_TRIGGERS_CONTENT_NAME)}
			/>
			<VisibilitySettings
				className={contentStyles.content}
				tags={tags}
				dealerId={dealerId}
				hasAlias={hasAlias}
				replaceTag={replaceTag}
				addTag={addTag}
				removeTag={removeTag}
				activeBrandWebsites={activeBrandWebsites}
				hasDealerWebsite={hasDealerWebsite}
				isAmagDealer={isAmagDealer}
				userPermissions={userPermissions}
				onClose={() => closeSettings(VISIBILITY_CONTENT_NAME)}
			/>
			{config.showFlagText && (
				<FlagSettings
					className={contentStyles.content}
					text={flagText}
					color={flagColor}
					backgroundColor={flagBackground}
					defaultColor={config.defaultFlagColor}
					defaultBackgroundColor={config.defaultFlagBackgroundColor}
					onChangeText={text =>
						updateDraft(['article', 'simple', 'flagText'], text)
					}
					onChangeBackgroundColor={color =>
						updateDraft(['article', 'simple', 'flagBackgroundColor'], color)
					}
					onClose={() => closeSettings(FLAG_CONTENT_NAME)}
				/>
			)}
		</div>
	);
};

const withHocs = combineHOCs([
	localize(namespace),
	withStyles(styles, contentStyles),
	connect((state: ReduxState) => ({
		brand: getBrand(state),
	})),
]);

export default (withHocs(PublishDialog): ReactComponent<Props>);
