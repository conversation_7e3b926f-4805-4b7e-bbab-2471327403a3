/* @flow */
import React from 'react';
import combineHOCs from '../../../../../tango/hoc/combineHOCs';
import { localize } from '../../../../../modules/i18n';
import { LocalizeProps } from '../../../../../modules/i18n/types';
import ValueSelect from './ValueSelect';
import { namespace } from './i18n';

export const POSITION_TOP: 'top' = 'top';
export const POSITION_MIDDLE: 'middle' = 'middle';
export const POSITION_BOTTOM: 'bottom' = 'bottom';

type Props = {
	position?: typeof POSITION_TOP | typeof POSITION_MIDDLE | typeof POSITION_BOTTOM,
	onChangeHorizontalPosition: Function,
	disabled?: boolean,
	availablePositions?: Array<string>,
};

const defaultHorizontalPositions = [
	POSITION_TOP,
	POSITION_MIDDLE,
	POSITION_BOTTOM,
];

function TitleHorizontalPositionSelect(props: Props & LocalizeProps) {
	const {
		position = POSITION_BOTTOM,
		onChangeHorizontalPosition,
		disabled = false,
		t,
		availablePositions = defaultHorizontalPositions,
	} = props;

	const horizontalPositions = availablePositions.map(pos => ({
		value: pos,
		label: t(pos),
	}));

	return (
		<ValueSelect
			values={horizontalPositions}
			selectedValue={position}
			label={t('titlePositionLabel')}
			onChange={selectedPosition => onChangeHorizontalPosition(selectedPosition)}
			disabled={disabled}
		/>
	);
}

const withHocs = combineHOCs([
	localize(namespace),
]);

export default (withHocs(TitleHorizontalPositionSelect): ReactComponent<Props>);
