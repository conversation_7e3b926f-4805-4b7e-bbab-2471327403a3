/* @flow */
const namespace = 'amag.modules.editorMenuDialogs.publishDialog';
const phrases = {
	de: {
		publishButton: 'Publiziere ab %{publishDate}',
		publishWithApproval: 'Auf Staging publizieren',
		no: 'Nein',
		showOnAllAmagDealers: 'all<PERSON> (ohne AOC, ACC und Dachseiten freie Händler)',
		neverPublishedSummary: 'Dieser Beitrag wurde noch nie publiziert.',
		publicationDateWithUnpublishDate: '%{fromDate} bis %{toDate}',
		'mein-skoda': 'Mein <PERSON>',
		publishUntilLabel: 'Deaktivieren ab:',
		showAsHighlight: 'Als Highlight anzeigen',
		dealersSelectionTitle: 'Bearbeitungs-Rechte',
		articleRightsSelectionTitle: 'Diritti di editing',
		articleRightsSelectionHint: 'Nur ein SuperAdmin kann diese Einstellungen ändern.',
		marketing: 'Marketing',
		back: 'Zur<PERSON>',
		yes: 'Ja',
		publicationSummaryBeforeDate: 'Publiziere ab',
		preview: 'Vorschau',
		publicationStatePublished: 'publiziert',
		publishSinceLabel: 'Publiziert ab:',
		amagWebsite: 'AMAG Webseite',
		dealerPromotions: 'Angebote',
		edit: '(editieren)',
		aboutus: 'Über uns',
		rights: {
			edit: {
				amagAdmin: 'Seulement SuperAdmin',
			},
		},
		settingsDialog: {
			title: 'Navigation personalisieren',
			save: 'Speichern',
			abort: 'Abbrechen',
		},
		showOnAllAmagRetailers: 'AMAG Retail',
		showOnAllNonAmagRetailers: 'Alle Partner - ausser AMAG Retail',
		never: 'nie',
		noFlag: 'Kein Störer-Balken definiert.',
		invisible: 'unsichtbar',
		background: 'Hintergrundfarbe',
		promotions: 'Angebote & Aktionen',
		more: 'Mehr',
		'worth-knowing': 'Wissenswertes',
		publicationStateUnpublished: 'deaktiviert',
		alias: {
			url: 'URL',
			urlAlreadyTaken: 'URL schon besetzt',
			conflictText: 'Leider ist diese URL schon besetzt. Bitte geben Sie eine andere URL ein.',
			abort: 'Abbrechen',
		},
		startpage: 'Startseite',
		servicePartner: 'Service Partner',
		actualPublicationStateSummary: 'Dieser Beitrag wurde am %{lastModified} %{publicationState}.',
		showOnThisDealer: 'nur hier',
		news: 'News',
		homepage: 'Homepage',
		siteSelectionTitle: 'Auf welchen Webseiten?',
		visibilitySummary: 'Publiziere auf: ',
		service: 'Service & Zubehör',
		showOnAllDealers: 'universum (bitte nicht benutzen)',
		sort: {
			AttributePlaceholder: 'Position',
			infoSortOrder: 'Bitte legen Sie eine globale Position fest.',
			noSortIndex: 'Keine Position festlegen',
		},
		jobs: 'Jobs',
		now: 'jetzt',
		selectLabel: 'Wo soll der Inhalt erscheinen?',
		publishNowButton: 'Publiziere',
		dealer: 'Händler',
		flagText: 'Text',
		dealersSelectionHint: 'Die Sichtbarkeit kann nicht mehr geändert werden, wenn eine merkbare URL angegeben ist.',
		flagTextPlaceholder: 'Text',
		website: 'Webseite',
		dealerService: 'Dienstleistung',
		languageLabel: 'Für welche Sprache?',
		contractSelectionTitle: 'Bei welchen Vertragspartnern?',
		wholesale: 'Firmenkunden',
		engagement: 'Engagement',
		languageHint: 'Die Sprache kann nicht mehr geändert werden, wenn eine merkbare URL angegeben ist.',
		aliasHint: 'URL wird bereits verwendet. Bitte Url anpassen',
		flagTextTitle: 'Störer-Balken',
		titlePositionLabel: 'Titel: ',
		left: 'Links',
		right: 'Rechts',
		center: 'Zentriert',
		top: 'Oben',
		middle: 'Mittig',
		bottom: 'Unten',
		dealerGroup: 'Gruppe',
		managedGroup: 'Betreute Gruppe',
		dealerGroupSite: 'Verbundsseite',
		dealerGroupNavigation: {
			news: 'Startseite',
			service: 'Dienstleistungen',
			promotions: 'Angebote',
			marketing: 'Marketing',
		},
		titleVisibityLabel: 'Titel im Highlight anzeigen',
		titleSize: {
			label: 'Schriftgrösse des Titels',
			values: {
				regular: 'Normal',
				small: 'Klein',
			},
		},
		titleColor: {
			label: 'Schriftfarbe des Titels',
			values: {
				white: 'Weiss',
				black: 'Schwarz',
			},
		},
		currentoffers: 'Aktuelle Angebote',
		serviceoffers: 'Serviceangebote',
		salesoffers: 'Verkaufsangebote',
		unpublishDateInPast: 'Deaktivierungsdatum liegt in der Vergangenheit',
		unpublishDateMissing: 'Bitte setzen Sie ein Deaktivierungsdatum',
	},
	fr: {
		publishButton: 'Publier à partir %{publishDate}',
		publishWithApproval: 'Publier en staging',
		no: 'Non',
		showOnAllAmagDealers: 'Afficher sur tous les sites de concessionnaires (sans AOC, ACC)',
		neverPublishedSummary: 'Cet article n’a jamais été publié',
		publicationDateWithUnpublishDate: '%{fromDate} à %{toDate}',
		'mein-skoda': 'Ma ŠKODA',
		publishUntilLabel: 'à',
		showAsHighlight: 'Montrer en tant qu’Highlight',
		dealersSelectionTitle: 'Quelles sont les entreprises concernées?',
		articleRightsSelectionTitle: 'Droits d\'édition',
		articleRightsSelectionHint: 'Seulement un SuperAdmin peut modifier ces paramètres.',
		marketing: 'Marketing',
		back: 'arrière',
		yes: 'Oui',
		publicationSummaryBeforeDate: 'Publie à partir de',
		preview: 'Aperçu',
		publicationStatePublished: 'publié',
		publishSinceLabel: 'Publié à partir de:',
		amagWebsite: 'Site web de AMAG',
		dealerPromotions: 'Offres',
		edit: '(éditer)',
		aboutus: 'L\'entreprise',
		rights: {
			edit: {
				amagAdmin: 'Nur SuperAdmin',
			},
		},
		settingsDialog: {
			title: 'Personnaliser la navigation',
			save: 'Enregistrer',
			abort: 'Annuler',
		},
		showOnAllAmagRetailers: 'AMAG Retail',
		showOnAllNonAmagRetailers: 'Tous les partenaires - sauf AMAG Retail',
		never: 'jamais',
		noFlag: 'Aucune texte bannière définie.',
		invisible: 'invisible',
		background: 'Couleur de fond',
		promotions: 'Offres & promotions',
		more: 'Plus',
		'worth-knowing': 'Wissenswertes',
		publicationStateUnpublished: 'désactivé',
		alias: {
			url: 'URL',
			urlAlreadyTaken: 'URL déjà utilisée',
			conflictText: 'Cette URL est malheureusement déjà utilisée. Veuillez choisir une autre URL.',
			abort: 'Annuler',
		},
		startpage: 'Page d’accueil',
		servicePartner: 'Partenaire de service',
		actualPublicationStateSummary: 'Cet article a été %{publicationState} à %{lastModified}',
		showOnThisDealer: 'seulement ici',
		news: 'News',
		homepage: 'Homepage',
		siteSelectionTitle: 'Sur quels sites web?',
		visibilitySummary: 'Publier sur: ',
		service: 'Service & Accessoires',
		showOnAllDealers: 'univers (ne pas utiliser)',
		sort: {
			AttributePlaceholder: 'Position',
			infoSortOrder: 'Veuillez entrer une position globale.',
			noSortIndex: 'Ne pas définir de position',
		},
		jobs: 'Jobs',
		now: 'maintenant',
		selectLabel: 'À quel endroit le contenu doit-il s\'afficher?',
		publishNowButton: 'Publies maintenant',
		dealer: 'Commerçant',
		flagText: 'Texte',
		dealersSelectionHint: 'La visibilité ne peut pas être changée lorsqu’une URL notable est spécifiée.',
		flagTextPlaceholder: 'Texte',
		website: 'Website',
		dealerService: 'Services',
		languageLabel: 'Pour quelle langue?',
		contractSelectionTitle: 'Quels partenaires contractuels?',
		wholesale: 'Clients entreprises',
		engagement: 'Engagements',
		languageHint: 'La langue ne peut pas être changée lorsqu’une URL notable est spécifiée',
		aliasHint: "L'URL est déjà utilisée. Veuillez l'adapter.",
		flagTextTitle: 'Texte bannière',
		titlePositionLabel: 'Titre: ',
		left: 'à gauche',
		right: 'à droite',
		center: 'Centre',
		top: 'Haut',
		middle: 'Milieu',
		bottom: 'Bas',
		dealerGroup: 'Groupe',
		managedGroup: 'Groupe supervisé',
		dealerGroupSite: 'Verbundsseite',
		dealerGroupNavigation: {
			news: 'Page de démarrage',
			service: 'Services',
			promotions: 'promos',
			marketing: 'Marketing',
		},
		titleVisibityLabel: 'Afficher le titre en évidence',
		titleSize: {
			label: 'Taille du titre',
			values: {
				regular: 'Normale',
				small: 'Petite',
			},
		},
		titleColor: {
			label: 'Couleur du titre',
			values: {
				white: 'Blanc',
				black: 'Noir',
			},
		},
		currentoffers: 'Offres actuelles',
		serviceoffers: 'Offres de services',
		salesoffers: 'Offres commerciales',
		unpublishDateInPast: 'La date de désactivation est dans le passé',
		unpublishDateMissing: 'Veuillez définir une date de désactivation',
	},
	it: {
		publishButton: 'Pubblica da %{publishDate}',
		publishWithApproval: 'Pubblica in staging',
		no: 'No',
		showOnAllAmagDealers: 'Visibile a tutti i commercianti (senza AOC, ACC)',
		neverPublishedSummary: 'Questo post è mai stato pubblicato.',
		publicationDateWithUnpublishDate: '%{fromDate} fino %{toDate}',
		'mein-skoda': 'La mia ŠKODA',
		publishUntilLabel: 'Disattivato da:',
		showAsHighlight: 'Mostra evidenziato',
		dealersSelectionTitle: 'Quali aziende sono colpite?',
		articleRightsSelectionTitle: 'Diritti di editing',
		articleRightsSelectionHint: 'Solo un SuperAdmin può modificare queste impostazioni.',
		marketing: 'Marketing',
		back: 'precedente',
		yes: 'Si',
		publicationSummaryBeforeDate: 'Pubblica al',
		preview: 'Anteprima',
		publicationStatePublished: 'pubblicato',
		publishSinceLabel: 'Pubblicato da:',
		amagWebsite: 'Sito web di AMAG',
		dealerPromotions: 'Offerte',
		edit: '(modificare)',
		aboutus: 'Chi siamo',
		rights: {
			edit: {
				amagAdmin: 'Solo SuperAdmin',
			},
		},
		settingsDialog: {
			title: 'Modifica navigazione',
			save: 'Salva',
			abort: 'Annulla',
		},
		showOnAllAmagRetailers: 'AMAG Retail',
		showOnAllNonAmagRetailers: 'Tutti i partner - eccetto AMAG Retail',
		never: 'mai',
		noFlag: 'Nessun banner di testo definito',
		invisible: 'invisibile',
		background: 'Colore di sfondo',
		promotions: 'Offerte & Promozioni',
		more: 'Di più',
		'worth-knowing': 'Wissenswertes',
		publicationStateUnpublished: 'disattivato',
		alias: {
			url: 'URL',
			urlAlreadyTaken: 'URL già assegnato',
			conflictText: 'Purtroppo questo URL è già stato assegnato. Si prega di inserire un altro URL.',
			abort: 'Annulla',
		},
		startpage: 'Start Page',
		servicePartner: 'Partner di servizio',
		actualPublicationStateSummary: 'Questo post e stato pubblicato al %{lastModified} %{publicationState}.',
		showOnThisDealer: 'solo qui',
		news: 'News',
		homepage: 'Homepage',
		siteSelectionTitle: 'Su quali siti web?',
		visibilitySummary: 'Pubblica su: ',
		service: 'Servizio & Accessori',
		showOnAllDealers: 'universo (non utilizzare)',
		sort: {
			AttributePlaceholder: 'Posizione',
			infoSortOrder: 'inserire una posizione globale.',
			noSortIndex: 'Non impostare una posizione',
		},
		jobs: 'Jobs',
		now: 'adesso',
		selectLabel: 'Dove dovrebbe apparire il contenuto?',
		publishNowButton: 'Pubblica',
		dealer: 'Commerciante',
		flagText: 'Testo',
		dealersSelectionHint: 'La visibilità non si puo piu cambiare dopo aver scelto una URL percepibile.',
		flagTextPlaceholder: 'Testo',
		website: 'Website',
		dealerService: 'Services',
		languageLabel: 'Per quale lingua?',
		contractSelectionTitle: 'Quali partner contrattuali?',
		wholesale: 'Clientela istituzionale',
		engagement: 'Impegno',
		languageHint: 'La lingua non si puo piu cambiare dopo aver scelto una URL percepibile.',
		aliasHint: "URL già in uso. si prega di modificare l'URL",
		flagTextTitle: 'Banner di testo',
		titlePositionLabel: 'Titolo: ',
		left: 'a sinistra',
		right: 'a destra',
		center: 'al centro',
		top: 'in alto',
		middle: 'in mezzo',
		bottom: 'in basso',
		dealerGroup: 'Gruppo',
		managedGroup: 'Gruppo supervisionato',
		dealerGroupSite: 'Verbundsseite',
		dealerGroupNavigation: {
			news: 'Pagina iniziale',
			service: 'Servizi',
			promotions: 'Offerte',
			marketing: 'Marketing',
		},
		titleVisibityLabel: 'Mostra il titolo in evidenza',
		titleSize: {
			label: 'Titolo Dimensione',
			values: {
				regular: 'Normale',
				small: 'Piccolo',
			},
		},
		titleColor: {
			label: 'Titolo Colore',
			values: {
				white: 'Bianco',
				black: 'Nero',
			},
		},
		currentoffers: 'Offerte attuali',
		serviceoffers: 'Offerte di servizio',
		salesoffers: 'Offerte di vendita',
		unpublishDateInPast: 'La data di disattivazione è nel passato',
		unpublishDateMissing: 'Si prega di impostare una data di disattivazione',
	},
};

export {
	phrases,
	namespace,
};
